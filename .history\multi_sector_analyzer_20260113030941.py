#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تحليل البيانات متعدد القطاعات
Multi-Sector Data Analysis System
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash, send_file, Response
from functools import wraps
import traceback
import unicodedata
import re
from typing import Dict, Any

# Setup logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our modules
try:
    from database_manager import init_database_manager, get_db_manager  # type: ignore
except Exception:
    # Provide fallbacks so the module can be imported in lightweight environments
    class MockDatabaseManager:
        def get_connection(self):
            # Return a mock context manager that does nothing
            from contextlib import contextmanager
            @contextmanager
            def mock_connection():
                yield None
            return mock_connection()

    def init_database_manager(config) -> 'MockDatabaseManager':
        return MockDatabaseManager()
    def get_db_manager() -> 'MockDatabaseManager':
        return MockDatabaseManager()

try:
    from cache_manager import get_cache_manager
except Exception:
    def get_cache_manager():
        return None

try:
    from memory_monitor import get_performance_optimizer
except Exception:
    # memory monitoring is optional; allow application to import without psutil
    def get_performance_optimizer(*args, **kwargs):
        return None

try:
    from sector_manager import SectorManager
except Exception:
    class SectorManager:
        def __init__(self, *args, **kwargs):
            pass

try:
    from security_manager import SecurityManager
except Exception:
    class SecurityManager:
        def __init__(self, *args, **kwargs):
            pass

try:
    from streaming_processor import StreamingDataProcessor, process_large_file
except Exception:
    # Provide fallback for streaming processor with basic functionality
    class StreamingDataProcessor:
        def __init__(self, *args, **kwargs):
            pass
        def process_large_file(self, file_path: str, processing_func, output_path=None, **kwargs):
            try:
                import pandas as pd
                if file_path.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(file_path, engine='openpyxl')
                else:
                    df = pd.read_csv(file_path)
                result = processing_func(df, **kwargs)
                return {
                    'success': True,
                    'method': 'fallback_full_load',
                    'rows_processed': len(df),
                    'result': result
                }
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Fallback processing failed: {str(e)}'
                }
        def _get_memory_usage(self):
            """Fallback memory usage method"""
            return 0.0
    def process_large_file(*args, **kwargs):
        return {'success': False, 'error': 'Streaming processor not available'}

from config import *

# Import export libraries
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("[EXPORT] ReportLab not available - PDF export disabled")

try:
    import xlsxwriter
    XLSXWRITER_AVAILABLE = True
except ImportError:
    XLSXWRITER_AVAILABLE = False
    print("[EXPORT] XlsxWriter not available - Excel export disabled")

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("[ANALYSIS] OpenPyXL not available - Excel file reading may be limited")

try:
    import weasyprint
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError) as exc:
    WEASYPRINT_AVAILABLE = False
    print(
        "[EXPORT] WeasyPrint not available - Web page PDF export disabled. "
        "Reason: {}".format(exc)
    )
    print("[EXPORT] Note: WeasyPrint requires GTK+ libraries on Windows. Use ReportLab or Playwright for PDF export instead.")

try:
    # Playwright gives the best parity with Chromium rendering
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except Exception:
    PLAYWRIGHT_AVAILABLE = False

# استيراد محرك التعلم الذكي
try:
    from ai_learning_engine import get_ai_learning_engine, AILearningEngine
    AI_LEARNING_AVAILABLE = True
    logger.info("[AI_LEARNING] تم تحميل محرك التعلم الذكي بنجاح")
except ImportError as e:
    AI_LEARNING_AVAILABLE = False
    logger.warning(f"[AI_LEARNING] لم يتم تحميل محرك التعلم الذكي: {e}")

def get_safe_ai_learning_engine():
    """دالة آمنة للحصول على محرك التعلم الذكي"""
    if AI_LEARNING_AVAILABLE:
        try:
            return get_ai_learning_engine()
        except Exception as e:
            logger.error(f"[AI_LEARNING] خطأ في الحصول على محرك التعلم الذكي: {e}")
            return None
    return None




# Arabic text processing functions
def fix_arabic_text(text):
    """Enhanced function to fix Arabic text that may be inverted or improperly encoded"""
    if not isinstance(text, str):
        return text

    original_text = text

    # Remove any BOM characters that might cause issues
    text = text.replace('\ufeff', '').replace('\u200e', '').replace('\u200f', '')

    # Check if text contains Arabic characters
    has_arabic = bool(re.search(r'[\u0600-\u06FF]', text))

    if has_arabic:
        # For Arabic text, ensure proper RTL display
        # Remove extra spaces and normalize
        text = re.sub(r'\s+', ' ', text.strip())

        # Comprehensive dictionary of known reversed patterns
        known_reversed_patterns = {
            # الكلمات المذكورة في المشكلة الأصلية
            'عونلا': 'النوع',
            'عرفلا': 'الفرع',

            # الكلمات الجديدة المذكورة في المشكلة
            'ةلودلا': 'الدولة',
            'ةظفاحملا': 'المحافظة',
            'ةيسنجلا': 'الجنسية',
            'ةيناثلا هجوزلا مسا': 'اسم الزوجة الثانية',
            'هجوزلا مسا': 'اسم الزوجة',
            'ةيناثلا': 'الثانية',

            # كلمات شائعة أخرى
            'ناونعلا': 'العنوان',
            'مسالا': 'الاسم',
            'خيراتلا': 'التاريخ',
            'ةنيدملا': 'المدينة',
            'فتاهلا': 'الهاتف',
            'ديربلا': 'البريد',
            'سنجلا': 'الجنس',
            'رمعلا': 'العمر',
            'ةفيظولا': 'الوظيفة',
            'بتارلا': 'الراتب',
            'مسقلا': 'القسم',
            'ةكرشلا': 'الشركة',
            'ةسسؤملا': 'المؤسسة',
            'ةقطنملا': 'المنطقة',
            'دلبلا': 'البلد',
            'ةمدخلا': 'الخدمة',
            'جتنملا': 'المنتج',
            'ةعلسلا': 'السلعة',
            'رعسلا': 'السعر',
            'ةميقلا': 'القيمة',
            'ددعلا': 'العدد',
            'ةيمكلا': 'الكمية',
            'نزولا': 'الوزن',
            'لوطلا': 'الطول',
            'ضرعلا': 'العرض',
            'عافترالا': 'الارتفاع',
            'مجحلا': 'الحجم',
            'ةحاسملا': 'المساحة',
            'ةفاسملا': 'المسافة',
            'تقولا': 'الوقت',
            'ةعاسلا': 'الساعة',
            'ةقيقدلا': 'الدقيقة',
            'مويلا': 'اليوم',
            'رهشلا': 'الشهر',
            'ةنسلا': 'السنة',
            'عوبسالا': 'الأسبوع',

            # كلمات إضافية للزواج والعائلة
            'جوزلا': 'الزوج',
            'ةجوزلا': 'الزوجة',
            'لوالا جوزلا': 'الزوج الأول',
            'ىلوالا ةجوزلا': 'الزوجة الأولى',
            'ثلاثلا جوزلا': 'الزوج الثالث',
            'ةثلاثلا ةجوزلا': 'الزوجة الثالثة',
            'عبارلا جوزلا': 'الزوج الرابع',
            'ةعبارلا ةجوزلا': 'الزوجة الرابعة'
        }

        # Check for exact matches first - ONLY fix known reversed patterns
        if text in known_reversed_patterns:
            logger.info(f"[ARABIC_FIX] Found exact reversed pattern: {text} -> {known_reversed_patterns[text]}")
            return known_reversed_patterns[text]

        # Check if text is already in correct form (appears in the values of known patterns)
        correct_patterns = set(known_reversed_patterns.values())
        if text in correct_patterns:
            logger.info(f"[ARABIC_FIX] Text is already correct: {text}")
            return text

        # Check for partial matches within the text
        for reversed_pattern, correct_pattern in known_reversed_patterns.items():
            if reversed_pattern in text:
                logger.info(f"[ARABIC_FIX] Found reversed pattern within text: {reversed_pattern} in {text}")
                text = text.replace(reversed_pattern, correct_pattern)
                return text

        # If no known patterns found, return text unchanged
        logger.info(f"[ARABIC_FIX] No known reversed patterns found, keeping text unchanged: {text}")
        return text

    return text

def process_arabic_column_names(df):
    """Centralized function to process Arabic column names in DataFrame using our enhanced fix_arabic_text function"""
    fixed_columns = []
    for col in df.columns:
        if isinstance(col, str) and any('\u0600' <= char <= '\u06FF' for char in col):
            # Use our enhanced fix_arabic_text function
            fixed_col = fix_arabic_text(col)
            fixed_columns.append(fixed_col)

            if fixed_col != col:
                logger.info(f"Fixed Arabic column name: '{col}' -> '{fixed_col}'")
            else:
                logger.info(f"Arabic column name already correct: '{col}'")
        else:
            fixed_columns.append(col)

    # Update DataFrame columns with fixed names
    df.columns = fixed_columns
    return df

def process_dataframe_arabic_text(df):
    """Process all text columns in a DataFrame to fix Arabic text issues"""
    logger.info(f"[DATAFRAME_PROCESSING] Starting Arabic text processing for DataFrame with shape: {df.shape}")
    logger.info(f"[DATAFRAME_PROCESSING] Columns: {list(df.columns)}")

    df_processed = df.copy()

    for col in df_processed.columns:
        logger.info(f"[DATAFRAME_PROCESSING] Processing column '{col}' with dtype: {df_processed[col].dtype}")
        if df_processed[col].dtype == 'object':  # Text columns
            logger.info(f"[DATAFRAME_PROCESSING] Applying Arabic text fixes to column '{col}'")
            original_sample = df_processed[col].dropna().head(3).tolist()
            logger.info(f"[DATAFRAME_PROCESSING] Original sample values for '{col}': {original_sample}")

            # Apply fix_arabic_text only to non-null values, keep nulls as null
            df_processed[col] = df_processed[col].where(
                df_processed[col].isnull(),
                df_processed[col].astype(str).apply(fix_arabic_text)
            )

            processed_sample = df_processed[col].dropna().head(3).tolist()
            logger.info(f"[DATAFRAME_PROCESSING] Processed sample values for '{col}': {processed_sample}")

            # Validate processing
            for i, (orig, proc) in enumerate(zip(original_sample, processed_sample)):
                if orig != proc:
                    logger.info(f"[DATAFRAME_PROCESSING] Column '{col}' value {i} changed: '{orig}' -> '{proc}'")

    logger.info(f"[DATAFRAME_PROCESSING] Completed Arabic text processing")
    return df_processed


def validate_dataframe_columns(df, expected_min_columns=1):
    """Validate DataFrame columns for consistency"""
    if df is None or df.empty:
        raise ValueError("DataFrame is empty or None")
        
    if len(df.columns) < expected_min_columns:
        raise ValueError(f"DataFrame has {len(df.columns)} columns, expected at least {expected_min_columns}")
        
    # Check for duplicate column names
    if len(df.columns) != len(set(df.columns)):
        duplicates = [col for col in df.columns if list(df.columns).count(col) > 1]
        logger.warning(f"Duplicate column names found: {duplicates}")
        
    # Check for empty column names
    empty_cols = [i for i, col in enumerate(df.columns) if not str(col).strip()]
    if empty_cols:
        logger.warning(f"Empty column names at positions: {empty_cols}")
        
    logger.info(f"DataFrame validation passed: {len(df.columns)} columns")
    return True



def validate_arabic_text_processing(text):
    """Validate that Arabic text processing worked correctly"""
    if not isinstance(text, str) or not text:
        return True  # Empty text is valid

    # Check for common Arabic processing issues
    has_arabic = bool(re.search(r'[\u0600-\u06FF]', text))

    if has_arabic:
        # Check for reversed text (common issue)
        if len(text) > 1:
            # If text starts with Arabic but contains Latin characters in wrong positions
            first_char_arabic = '\u0600' <= text[0] <= '\u06FF'
            last_char_arabic = '\u0600' <= text[-1] <= '\u06FF'

            # This is a heuristic - if text looks suspiciously reversed
            if first_char_arabic and not last_char_arabic and len(text) > 3:
                # Check if this might be reversed by looking at character patterns
                arabic_chars = sum(1 for c in text if '\u0600' <= c <= '\u06FF')
                if arabic_chars > len(text) * 0.3:  # More than 30% Arabic chars
                    logger.warning(f"Potential Arabic text processing issue detected: '{text}'")
                    return False

    return True

# Enhanced Outlier Detection Functions

def detect_numeric_outliers(col_data, col_name):
    """
    Comprehensive numeric outlier detection with advanced statistical and domain-specific validation
    """
    outliers = []
    col_lower = str(col_name).lower()

    try:
        # Remove null values for analysis
        clean_data = col_data.dropna()
        if len(clean_data) == 0:
            return []

        # === STATISTICAL OUTLIER DETECTION ===

        # 1. Enhanced IQR method with adaptive sensitivity
        if len(clean_data) > 4:
            Q1 = clean_data.quantile(0.25)
            Q3 = clean_data.quantile(0.75)
            IQR = Q3 - Q1

            # Adaptive multiplier based on data size and distribution
            if len(clean_data) > 1000:
                multiplier = 2.0  # More strict for large datasets
            elif len(clean_data) > 100:
                multiplier = 1.8
            else:
                multiplier = 1.5  # Standard IQR

            lower_bound = Q1 - multiplier * IQR
            upper_bound = Q3 + multiplier * IQR

            statistical_outliers = clean_data[(clean_data < lower_bound) | (clean_data > upper_bound)]
            outliers.extend(statistical_outliers.tolist())

        # 2. Z-score method for larger datasets
        if len(clean_data) > 30:
            mean_val = clean_data.mean()
            std_val = clean_data.std()
            if std_val > 0:
                z_scores = abs((clean_data - mean_val) / std_val)
                # Adaptive Z-score threshold
                z_threshold = 3.0 if len(clean_data) > 100 else 2.5
                z_outliers = clean_data[z_scores > z_threshold]
                outliers.extend(z_outliers.tolist())

        # 3. Modified Z-score using median (more robust)
        if len(clean_data) > 10:
            median_val = clean_data.median()
            mad = abs(clean_data - median_val).median()  # Median Absolute Deviation
            if mad > 0:
                modified_z_scores = 0.6745 * (clean_data - median_val) / mad
                mad_outliers = clean_data[abs(modified_z_scores) > 3.5]
                outliers.extend(mad_outliers.tolist())

        # === DOMAIN-SPECIFIC VALIDATIONS ===

        # Age and Year validation
        if any(keyword in col_lower for keyword in ['عمر', 'age', 'سن', 'موديل', 'model', 'سنة', 'year']):
            # Age validation
            if any(keyword in col_lower for keyword in ['عمر', 'age', 'سن']):
                invalid_ages = clean_data[(clean_data < 0) | (clean_data > 150)]
                outliers.extend(invalid_ages.tolist())

        # Insurance-specific validation for AM field (mother's age)
        if any(keyword in col_lower for keyword in ['الأم', 'am', 'mother']) and 'insurance' in col_lower:
            # Specific anomalous values and patterns for insurance AM field
            anomalous_am_values = [26.0, 29.0, 31.0]  # The specified anomalous ages
            anomalous_am_patterns = [
                '26 26 26', '29 29 29', '31 31 31',  # Repeated number patterns
                'صن صن صن'  # Arabic text pattern that may indicate data entry error
            ]

            for val in col_data:  # Check all values, not just clean_data
                val_str = str(val).strip()

                # Check for exact numeric matches
                try:
                    val_float = float(val)
                    if val_float in anomalous_am_values:
                        outliers.append(val)
                        logger.info(f"[OUTLIER_DETECTION] Insurance AM field: Detected anomalous numeric value {val_float}")
                        continue
                except (ValueError, TypeError):
                    pass

                # Check for anomalous text patterns
                for pattern in anomalous_am_patterns:
                    if pattern in val_str:
                        outliers.append(val)
                        logger.info(f"[OUTLIER_DETECTION] Insurance AM field: Detected anomalous pattern '{pattern}' in value '{val_str}'")
                        break

            # Model year validation
            if any(keyword in col_lower for keyword in ['موديل', 'model', 'سنة']):
                current_year = datetime.now().year
                unrealistic_years = clean_data[(clean_data < 1800) | (clean_data > current_year + 5)]
                outliers.extend(unrealistic_years.tolist())

        # Percentage and Rate validation
        elif any(keyword in col_lower for keyword in ['%', 'نسبة', 'percentage', 'معدل', 'rate', 'تغير']):
            # Standard percentage (0-100)
            if 'نسبة' in col_lower or '%' in col_name:
                invalid_percentages = clean_data[(clean_data < -100) | (clean_data > 200)]  # Allow some flexibility
                outliers.extend(invalid_percentages.tolist())
            # Exchange rates and multipliers
            elif 'معدل' in col_lower:
                invalid_rates = clean_data[(clean_data <= 0) | (clean_data > 1000)]
                outliers.extend(invalid_rates.tolist())

        # Financial and Monetary validation
        elif any(keyword in col_lower for keyword in ['قيمة', 'مبلغ', 'value', 'amount', 'salary', 'راتب', 'سعر', 'price', 'جمركية', 'ريال', 'أجنبي']):
            # Negative financial values (usually invalid)
            negative_values = clean_data[clean_data < 0]
            outliers.extend(negative_values.tolist())

            # Extremely large values (potential data entry errors)
            if 'ريال' in col_lower or 'جمركية' in col_lower:
                extreme_values = clean_data[clean_data > 1000000000]  # > 1 billion
            else:
                extreme_values = clean_data[clean_data > 100000000]   # > 100 million
            outliers.extend(extreme_values.tolist())

            # Suspiciously small non-zero values
            tiny_values = clean_data[(clean_data > 0) & (clean_data < 0.01)]
            outliers.extend(tiny_values.tolist())

        # Weight and Measurement validation
        elif any(keyword in col_lower for keyword in ['وزن', 'weight', 'كيلو', 'kg', 'طول', 'length', 'عرض', 'width']):
            # Negative weights/measurements
            negative_measurements = clean_data[clean_data < 0]
            outliers.extend(negative_measurements.tolist())

            # Unreasonably large measurements
            if 'وزن' in col_lower or 'weight' in col_lower:
                extreme_weights = clean_data[clean_data > 100000]  # > 100 tons
                outliers.extend(extreme_weights.tolist())

        # Quantity and Count validation
        elif any(keyword in col_lower for keyword in ['كمية', 'عدد', 'quantity', 'count', 'طرود', 'إيصال', 'صنف']):
            # Negative quantities
            negative_quantities = clean_data[clean_data < 0]
            outliers.extend(negative_quantities.tolist())

            # Unreasonably large quantities
            if any(keyword in col_lower for keyword in ['عدد', 'count']):
                large_quantities = clean_data[clean_data > 50000]  # > 50K items
                outliers.extend(large_quantities.tolist())

        # ID and Code validation
        elif any(keyword in col_lower for keyword in ['رقم', 'id', 'كود', 'code', 'ضريبي', 'بيان', 'منفذ', 'تعريفي']):
            # Tax IDs and similar should be positive
            if any(keyword in col_lower for keyword in ['ضريبي', 'tax']):
                invalid_tax_ids = clean_data[(clean_data <= 0) | (clean_data > *********)]
                outliers.extend(invalid_tax_ids.tolist())

            # Port codes and similar
            elif any(keyword in col_lower for keyword in ['منفذ', 'port', 'كود']):
                invalid_codes = clean_data[(clean_data < 0) | (clean_data > 99999)]
                outliers.extend(invalid_codes.tolist())

            # General ID validation
            else:
                invalid_ids = clean_data[clean_data < 0]
                outliers.extend(invalid_ids.tolist())

        # Phone and Communication validation
        elif any(keyword in col_lower for keyword in ['هاتف', 'phone', 'تلفون', 'جوال', 'فاكس']):
            # Convert to string for length validation
            str_values = clean_data.astype(str)
            # Phone numbers should have reasonable length
            invalid_phones = []
            for val in clean_data:
                str_val = str(int(val)) if val == int(val) else str(val)
                if len(str_val) < 7 or len(str_val) > 15:
                    invalid_phones.append(val)
            outliers.extend(invalid_phones)

        # === GENERAL NUMERIC QUALITY CHECKS ===

        # 1. Extreme values based on data distribution
        if len(clean_data) > 20:
            # Values that are more than 10 standard deviations from mean
            mean_val = clean_data.mean()
            std_val = clean_data.std()
            if std_val > 0:
                extreme_outliers = clean_data[abs(clean_data - mean_val) > 10 * std_val]
                outliers.extend(extreme_outliers.tolist())

        # 2. Repeated suspicious values
        if len(clean_data) > 50:
            value_counts = clean_data.value_counts()
            # Check for values that appear exactly once and are far from others
            singletons = value_counts[value_counts == 1].index
            if len(singletons) > 0:
                median_val = clean_data.median()
                mad = abs(clean_data - median_val).median()
                if mad > 0:
                    for singleton in singletons:
                        if abs(singleton - median_val) > 5 * mad:
                            outliers.append(singleton)

        # 3. Precision anomalies (too many decimal places)
        decimal_outliers = []
        for val in clean_data:
            if isinstance(val, float):
                str_val = str(val)
                if '.' in str_val:
                    decimal_places = len(str_val.split('.')[1])
                    if decimal_places > 6:  # More than 6 decimal places might be suspicious
                        decimal_outliers.append(val)
        outliers.extend(decimal_outliers)

        return list(set(outliers))  # Remove duplicates

    except Exception as e:
        logger.error(f"Error in comprehensive numeric outlier detection for {col_name}: {e}")
        return []

def detect_date_outliers(col_data, col_name):
    """
    Detect invalid dates and unrealistic date values
    """
    outliers = []
    current_year = datetime.now().year
    col_lower = str(col_name).lower()

    for val in col_data:
        try:
            # Try to convert to datetime
            date_val = pd.to_datetime(str(val), errors='coerce')

            if pd.isna(date_val):
                # Cannot convert to valid date
                outliers.append(val)
                continue

            year = date_val.year

            # Birth date validation
            if 'ميلاد' in col_lower or 'birth' in col_lower:
                age = current_year - year
                if age < 0:  # Future birth date
                    outliers.append(val)
                elif age > 150:  # Unrealistically old
                    outliers.append(val)
                elif age < 1:  # Too young (less than 1 year)
                    outliers.append(val)

            # General date validation
            elif 'تاريخ' in col_lower or 'date' in col_lower:
                if year < 1900:  # Too old
                    outliers.append(val)
                elif year > current_year + 10:  # Too far in future
                    outliers.append(val)

            # Expiry/end dates should not be in past (unless specified)
            elif any(keyword in col_lower for keyword in ['انتهاء', 'expiry', 'end', 'انتهاء الصلاحية']):
                if date_val < datetime.now() and 'سابق' not in col_lower:
                    outliers.append(val)

        except Exception:
            # Any parsing error means invalid date
            outliers.append(val)

    return outliers

def detect_name_outliers(col_data, col_name):
    """
    Detect invalid or suspicious names
    """
    outliers = []

    for val in col_data:
        val_str = str(val).strip()

        # Empty or too short
        if len(val_str) < 2:
            outliers.append(val)
            continue

        # Too long
        if len(val_str) > 100:
            outliers.append(val)
            continue

        # Contains numbers
        if re.search(r'\d', val_str):
            outliers.append(val)
            continue

        # Contains special characters (except spaces and hyphens)
        if re.search(r'[^\u0600-\u06FF\s\-]', val_str):
            outliers.append(val)
            continue

        # Too many words (more than 6)
        words = val_str.split()
        if len(words) > 6:
            outliers.append(val)
            continue

        # Words too short (less than 2 characters)
        if any(len(word) < 2 for word in words if word not in ['بن', 'بنت', 'أبو', 'أم']):
            outliers.append(val)
            continue

        # Excessive repetition (more than 5% of total records)
        if len(col_data) > 20:
            value_count = col_data.value_counts().get(val, 0)
            if value_count > len(col_data) * 0.05:  # More than 5%
                outliers.append(val)
                continue

    return outliers

def detect_text_outliers(col_data, col_name, limited_values=None):
    """
    Comprehensive text outlier detection with advanced validation criteria for all text data types
    """
    outliers = []
    col_lower = str(col_name).lower()

    # If field has limited values, anything outside them is outlier
    if limited_values and len(limited_values) > 0:
        for val in col_data:
            if str(val) not in limited_values:
                outliers.append(val)
        return outliers

    try:
        # Convert to string and get comprehensive statistics
        str_data = col_data.astype(str)
        lengths = str_data.str.len()
        mean_length = lengths.mean()
        std_length = lengths.std()
        median_length = lengths.median()

        # Calculate dynamic thresholds based on data distribution
        length_threshold_upper = mean_length + (2.5 * std_length) if std_length > 0 else mean_length * 2
        length_threshold_lower = max(1, mean_length - (2.5 * std_length)) if std_length > 0 else 1

        # Analyze character patterns in the dataset
        all_text = ' '.join(str_data.tolist())
        arabic_pattern = re.compile(r'[\u0600-\u06FF]')
        english_pattern = re.compile(r'[a-zA-Z]')
        number_pattern = re.compile(r'\d')

        # Enhanced text validation for different field types
        for val in col_data:
            if pd.isna(val):
                continue

            val_str = str(val).strip()

            # === BASIC QUALITY CHECKS ===

            # Empty or null-like values
            if not val_str or val_str.lower() in ['nan', 'null', 'none', '', 'n/a', '#n/a', 'غير محدد', 'لا يوجد']:
                outliers.append(val)
                continue

            # Whitespace anomalies
            if val_str != str(val).strip():  # Leading/trailing whitespace
                outliers.append(val)
                continue
            if re.search(r'\s{3,}', val_str):  # Multiple consecutive spaces
                outliers.append(val)
                continue

            # === FIELD-SPECIFIC VALIDATIONS ===

            # Brand/Make validation (الماركة)
            if any(keyword in col_lower for keyword in ['ماركة', 'brand', 'make', 'صانع', 'manufacturer']):
                # Length checks
                if len(val_str) > 60 or len(val_str) < 2:
                    outliers.append(val)
                    continue
                # Excessive special characters
                special_chars = re.findall(r'[^\w\s\u0600-\u06FF\-/&.]', val_str)
                if len(special_chars) > len(val_str) * 0.25:
                    outliers.append(val)
                    continue
                # Too many separators (concatenated data like the example)
                if val_str.count('/') > 3 or val_str.count('|') > 2:
                    outliers.append(val)
                    continue
                # Mixed inappropriate content
                if re.search(r'\d+[a-zA-Z\u0600-\u06FF]+\d+', val_str):
                    outliers.append(val)
                    continue

            # Model/Type validation (الموديل/النوع)
            elif any(keyword in col_lower for keyword in ['موديل', 'model', 'طراز', 'type', 'نوع']):
                # Excessive length
                if len(val_str) > 120:
                    outliers.append(val)
                    continue
                # Too many separators (indicates concatenated data like the example)
                separators = val_str.count('/') + val_str.count('|') + val_str.count('\\')
                if separators > 10:
                    outliers.append(val)
                    continue
                # Mixed inappropriate content (too many numbers)
                if re.search(r'\d{4,}', val_str) and len(re.findall(r'\d', val_str)) > len(val_str) * 0.5:
                    outliers.append(val)
                    continue

            # Name fields validation
            elif any(keyword in col_lower for keyword in ['اسم', 'name', 'الشخص', 'person', 'مستورد', 'importer', 'مخلص']):
                # Length checks
                if len(val_str) < 3 or len(val_str) > 100:
                    outliers.append(val)
                    continue
                # Excessive numbers in names
                if len(re.findall(r'\d', val_str)) > len(val_str) * 0.3:
                    outliers.append(val)
                    continue
                # Inappropriate special characters
                if re.search(r'[^\w\s\u0600-\u06FF\-.,()&]', val_str):
                    outliers.append(val)
                    continue

            # Address/location fields
            elif any(keyword in col_lower for keyword in ['عنوان', 'address', 'منطقة', 'مدينة', 'city', 'location', 'منفذ']):
                if len(val_str) > 200 or len(val_str) < 3:
                    outliers.append(val)
                    continue

            # Description/details fields
            elif any(keyword in col_lower for keyword in ['وصف', 'description', 'تفاصيل', 'details', 'ملاحظات', 'notes', 'سلعة']):
                if len(val_str) > 500:
                    outliers.append(val)
                    continue

            # Email validation
            elif any(keyword in col_lower for keyword in ['بريد', 'email', 'إيميل']):
                if '@' not in val_str or len(val_str) < 5 or ' ' in val_str or not re.match(r'^[^@]+@[^@]+\.[^@]+$', val_str):
                    outliers.append(val)
                    continue

            # Phone validation
            elif any(keyword in col_lower for keyword in ['هاتف', 'phone', 'تلفون', 'جوال']):
                # Should contain mostly numbers and valid phone characters
                if not re.match(r'^[\d\s\-\+\(\)\.]+$', val_str) or len(val_str) < 7 or len(val_str) > 20:
                    outliers.append(val)
                    continue

            # URL validation
            elif any(keyword in col_lower for keyword in ['موقع', 'url', 'website', 'رابط']):
                if not val_str.startswith(('http://', 'https://', 'www.')) or len(val_str) < 10:
                    outliers.append(val)
                    continue

            # ID/Code validation
            elif any(keyword in col_lower for keyword in ['رقم', 'كود', 'id', 'code', 'ضريبي', 'بيان', 'صنف', 'تعريفي']):
                # Should contain numbers for most ID fields
                if not re.search(r'\d', val_str) and len(val_str) > 5:  # Long text without numbers
                    outliers.append(val)
                    continue
                # Too short for meaningful IDs
                if len(val_str) < 2:
                    outliers.append(val)
                    continue

            # === GENERAL TEXT QUALITY CHECKS ===

            # 1. Excessive character repetition
            if re.search(r'(.)\1{5,}', val_str):  # 5+ repeated characters
                outliers.append(val)
                continue

            # 2. Unusual character patterns
            # Too many consecutive special characters
            if re.search(r'[^\w\s\u0600-\u06FF]{4,}', val_str):
                outliers.append(val)
                continue

            # 3. Encoding/corruption issues
            if any(char in val_str for char in ['�', '\x00', '\ufffd']):
                outliers.append(val)
                continue

            # 4. Statistical length outliers (for larger datasets)
            if len(col_data) > 30:
                if len(val_str) > length_threshold_upper or len(val_str) < length_threshold_lower:
                    outliers.append(val)
                    continue

            # 5. Script consistency checks
            if len(col_data) > 50:
                arabic_chars = len(arabic_pattern.findall(val_str))
                english_chars = len(english_pattern.findall(val_str))
                total_letters = arabic_chars + english_chars

                if total_letters > 0:
                    # Sample other values to find common patterns
                    sample_size = min(50, len(col_data))
                    sample_data = col_data.sample(sample_size) if hasattr(col_data, 'sample') else col_data[:sample_size]

                    arabic_majority = sum(1 for v in sample_data if arabic_pattern.search(str(v))) > sample_size * 0.7
                    english_majority = sum(1 for v in sample_data if english_pattern.search(str(v))) > sample_size * 0.7

                    # Flag if completely different from majority pattern
                    if arabic_majority and arabic_chars == 0 and english_chars > 0:
                        outliers.append(val)
                        continue
                    elif english_majority and english_chars == 0 and arabic_chars > 0:
                        outliers.append(val)
                        continue

            # 6. Frequency-based outliers (rare values in large datasets)
            if len(col_data) > 200:
                value_counts = col_data.value_counts()
                val_frequency = value_counts.get(val, 0)

                # Singleton values in very large datasets might be errors
                if val_frequency == 1 and len(value_counts) > len(col_data) * 0.3:
                    outliers.append(val)
                    continue

        return list(set(outliers))  # Remove duplicates

    except Exception as e:
        logger.error(f"Error in comprehensive text outlier detection for {col_name}: {e}")
        return []

def detect_outliers_comprehensive(col_data, col_name, field_settings=None):
    """
    Advanced comprehensive outlier detection for all data types with multi-layered validation
    """
    if col_data.empty or len(col_data) == 0:
        return []

    outliers = []
    col_lower = str(col_name).lower()

    # Get field settings
    limited_values = field_settings.get('limited_values', []) if field_settings else []

    try:
        # === PRIMARY TYPE-BASED DETECTION ===

        # Detect based on data type and field name
        if pd.api.types.is_numeric_dtype(col_data):
            outliers = detect_numeric_outliers(col_data, col_name)
        elif any(keyword in col_lower for keyword in ['تاريخ', 'ميلاد', 'date', 'birth', 'إيصال', 'تصفية', 'تسجيل']):
            outliers = detect_date_outliers(col_data, col_name)
        elif any(keyword in col_lower for keyword in ['اسم', 'name', 'مستورد', 'مخلص', 'شخص']):
            outliers = detect_name_outliers(col_data, col_name)
        else:
            outliers = detect_text_outliers(col_data, col_name, limited_values)

        # === UNIVERSAL DATA QUALITY CHECKS ===

        # 1. Null-like and empty values detection
        null_like_values = [
            '', 'null', 'NULL', 'nan', 'NaN', 'none', 'None', 'N/A', 'n/a', '#N/A', '#NULL!',
            'غير محدد', 'لا يوجد', 'فارغ', 'مفقود', 'غير متوفر', 'unknown', 'undefined', 'missing'
        ]

        for val in col_data:
            val_str = str(val).strip().lower()
            if val_str in [v.lower() for v in null_like_values]:
                if val not in outliers:
                    outliers.append(val)

        # 2. Encoding and corruption detection
        for val in col_data:
            val_str = str(val)
            # Check for encoding issues
            if any(char in val_str for char in ['�', '\x00', '\ufffd', '\ufeff']):
                if val not in outliers:
                    outliers.append(val)

            # Check for control characters
            if any(ord(char) < 32 and char not in ['\t', '\n', '\r'] for char in val_str):
                if val not in outliers:
                    outliers.append(val)

        # 3. Data consistency checks
        if len(col_data) > 10:
            # Check for values that are completely different in format from majority
            str_data = col_data.astype(str)

            # Pattern analysis
            patterns = {}
            for val in str_data:
                val_clean = str(val).strip()
                if val_clean:
                    # Categorize by basic pattern
                    if val_clean.isdigit():
                        pattern = 'digits_only'
                    elif val_clean.isalpha():
                        pattern = 'letters_only'
                    elif any(c.isdigit() for c in val_clean) and any(c.isalpha() for c in val_clean):
                        pattern = 'mixed_alphanumeric'
                    elif re.match(r'^[\d\s\-\+\(\)\.]+$', val_clean):
                        pattern = 'numeric_formatted'
                    elif re.match(r'^[a-zA-Z\u0600-\u06FF\s\-\.]+$', val_clean):
                        pattern = 'text_formatted'
                    else:
                        pattern = 'special_characters'

                    patterns[pattern] = patterns.get(pattern, 0) + 1

            # Find majority pattern
            if patterns:
                majority_pattern = max(patterns, key=patterns.get)
                majority_count = patterns[majority_pattern]

                # Flag values that don't match majority pattern and are rare
                if majority_count > len(col_data) * 0.6:  # 60% majority
                    for val in col_data:
                        val_str = str(val).strip()
                        if val_str:
                            # Check if this value matches minority pattern
                            current_pattern = None
                            if val_str.isdigit():
                                current_pattern = 'digits_only'
                            elif val_str.isalpha():
                                current_pattern = 'letters_only'
                            elif any(c.isdigit() for c in val_str) and any(c.isalpha() for c in val_str):
                                current_pattern = 'mixed_alphanumeric'
                            elif re.match(r'^[\d\s\-\+\(\)\.]+$', val_str):
                                current_pattern = 'numeric_formatted'
                            elif re.match(r'^[a-zA-Z\u0600-\u06FF\s\-\.]+$', val_str):
                                current_pattern = 'text_formatted'
                            else:
                                current_pattern = 'special_characters'

                            # If pattern is different from majority and rare
                            if current_pattern != majority_pattern and patterns.get(current_pattern, 0) < len(col_data) * 0.1:
                                if val not in outliers:
                                    outliers.append(val)

        # 4. Length-based anomaly detection for all data types
        if len(col_data) > 20:
            str_data = col_data.astype(str)
            lengths = str_data.str.len()

            # Statistical length outliers
            if lengths.std() > 0:
                mean_length = lengths.mean()
                std_length = lengths.std()

                # Values with extremely unusual lengths
                length_outliers = str_data[
                    (lengths > mean_length + 3 * std_length) |
                    (lengths < max(1, mean_length - 3 * std_length))
                ]

                for val in length_outliers:
                    original_val = col_data[str_data == val].iloc[0] if len(col_data[str_data == val]) > 0 else val
                    if original_val not in outliers:
                        outliers.append(original_val)

        # 5. Frequency-based outlier detection (enhanced)
        if len(col_data) > 50:
            value_counts = col_data.value_counts()
            total_values = len(col_data)
            unique_values = len(value_counts)

            # Detect singleton values in large datasets
            if unique_values > total_values * 0.3:  # High diversity
                singletons = value_counts[value_counts == 1]
                for singleton_val in singletons.index:
                    if singleton_val not in outliers:
                        outliers.append(singleton_val)

            # Detect values that appear with suspicious frequency
            expected_freq = total_values / unique_values
            for val, freq in value_counts.items():
                # Values that appear way too often (potential data duplication errors)
                if freq > expected_freq * 10 and freq > total_values * 0.5:
                    # Don't flag the value itself, but log it
                    logger.warning(f"[OUTLIER_DETECTION] Field '{col_name}': Value '{val}' appears {freq} times ({freq/total_values*100:.1f}%)")

        # 6. Cross-field consistency checks (if field settings available)
        if field_settings:
            expected_type = field_settings.get('field_type', '')
            if expected_type:
                for val in col_data:
                    val_str = str(val).strip()
                    if val_str:
                        # Check if value matches expected type
                        type_mismatch = False

                        if expected_type == 'numeric' and not re.match(r'^-?\d*\.?\d+$', val_str):
                            type_mismatch = True
                        elif expected_type == 'date' and not re.match(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', val_str):
                            type_mismatch = True
                        elif expected_type == 'email' and '@' not in val_str:
                            type_mismatch = True

                        if type_mismatch and val not in outliers:
                            outliers.append(val)

        # === FINAL PROCESSING ===

        # Remove duplicates and convert to list
        outliers = list(set(outliers))

        # Limit to reasonable number for performance
        if len(outliers) > 1000:
            outliers = outliers[:1000]
            logger.warning(f"[OUTLIER_DETECTION] Field '{col_name}': Limited outliers to 1000 (found {len(outliers)} total)")

        # Log detailed information
        if len(outliers) > 0:
            outlier_percentage = (len(outliers) / len(col_data)) * 100
            logger.info(f"[OUTLIER_DETECTION] Field '{col_name}': Found {len(outliers)} outliers out of {len(col_data)} values ({outlier_percentage:.1f}%)")
            logger.info(f"[OUTLIER_DETECTION] Examples: {[str(x) for x in outliers[:5]]}")
        else:
            logger.info(f"[OUTLIER_DETECTION] Field '{col_name}': No outliers detected in {len(col_data)} values")

    except Exception as e:
        logger.error(f"[OUTLIER_DETECTION] Error in comprehensive detection for '{col_name}': {e}")
        outliers = []

    return outliers

def detect_data_quality_issues(col_data, col_name):
    """
    Comprehensive data quality issue detection including duplicates, missing patterns, and inconsistencies
    """
    issues = {
        'duplicates': [],
        'missing_patterns': [],
        'inconsistent_formats': [],
        'suspicious_patterns': [],
        'data_entry_errors': []
    }

    try:
        col_lower = str(col_name).lower()
        str_data = col_data.astype(str)

        # === DUPLICATE DETECTION ===
        value_counts = col_data.value_counts()
        total_records = len(col_data)

        # Excessive duplicates
        for val, count in value_counts.items():
            if count > total_records * 0.3 and count > 10:  # More than 30% and at least 10 occurrences
                issues['duplicates'].append({
                    'value': str(val),
                    'count': int(count),
                    'percentage': round((count / total_records) * 100, 2)
                })

        # === MISSING PATTERN DETECTION ===
        # Sequential missing values (for ID fields)
        if any(keyword in col_lower for keyword in ['رقم', 'id', 'code', 'sequence']):
            numeric_values = []
            for val in col_data:
                try:
                    if pd.notna(val):
                        numeric_values.append(int(float(val)))
                except:
                    continue

            if len(numeric_values) > 10:
                numeric_values.sort()
                gaps = []
                for i in range(1, len(numeric_values)):
                    gap = numeric_values[i] - numeric_values[i-1]
                    if gap > 1:
                        gaps.append({
                            'from': numeric_values[i-1],
                            'to': numeric_values[i],
                            'gap_size': gap - 1
                        })

                # Report significant gaps
                for gap in gaps:
                    if gap['gap_size'] > 5:  # Missing more than 5 sequential values
                        issues['missing_patterns'].append(gap)

        # === FORMAT INCONSISTENCY DETECTION ===
        if len(col_data) > 20:
            # Group values by format patterns
            format_patterns = {}

            for val in str_data:
                val_clean = str(val).strip()
                if val_clean and val_clean.lower() not in ['nan', 'null', 'none']:
                    # Create format pattern
                    pattern = re.sub(r'\d', 'D', val_clean)  # Replace digits with D
                    pattern = re.sub(r'[a-zA-Z\u0600-\u06FF]', 'L', pattern)  # Replace letters with L
                    pattern = re.sub(r'\s', 'S', pattern)  # Replace spaces with S

                    format_patterns[pattern] = format_patterns.get(pattern, 0) + 1

            # Find inconsistent formats
            if len(format_patterns) > 1:
                total_formatted = sum(format_patterns.values())
                for pattern, count in format_patterns.items():
                    percentage = (count / total_formatted) * 100
                    if percentage < 10 and count < 5:  # Less than 10% and fewer than 5 occurrences
                        issues['inconsistent_formats'].append({
                            'pattern': pattern,
                            'count': count,
                            'percentage': round(percentage, 2),
                            'examples': [str(val) for val in str_data if re.sub(r'\d', 'D', re.sub(r'[a-zA-Z\u0600-\u06FF]', 'L', re.sub(r'\s', 'S', str(val).strip()))) == pattern][:3]
                        })

        # === SUSPICIOUS PATTERN DETECTION ===

        # 1. Keyboard patterns (qwerty, asdf, etc.)
        keyboard_patterns = ['qwerty', 'asdf', 'zxcv', '1234', '0000', 'aaaa', 'test', 'تست']
        for val in str_data:
            val_lower = str(val).lower().strip()
            for pattern in keyboard_patterns:
                if pattern in val_lower and len(val_lower) <= len(pattern) + 2:
                    issues['suspicious_patterns'].append({
                        'value': str(val),
                        'issue': f'Keyboard pattern detected: {pattern}'
                    })

        # 2. Repeated character patterns
        for val in str_data:
            val_str = str(val).strip()
            if len(val_str) > 3:
                # Check for repeated patterns
                if re.search(r'(.{2,})\1{2,}', val_str):  # Pattern repeated 3+ times
                    issues['suspicious_patterns'].append({
                        'value': str(val),
                        'issue': 'Repeated pattern detected'
                    })

        # 3. Sequential characters (abc, 123, etc.)
        for val in str_data:
            val_str = str(val).strip()
            if len(val_str) >= 3:
                # Check for sequential numbers
                if re.search(r'012|123|234|345|456|567|678|789', val_str):
                    issues['suspicious_patterns'].append({
                        'value': str(val),
                        'issue': 'Sequential numbers detected'
                    })

                # Check for sequential letters
                if re.search(r'abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz', val_str.lower()):
                    issues['suspicious_patterns'].append({
                        'value': str(val),
                        'issue': 'Sequential letters detected'
                    })

        # === DATA ENTRY ERROR DETECTION ===

        # 1. Mixed language inappropriately
        for val in str_data:
            val_str = str(val).strip()
            if len(val_str) > 5:
                arabic_chars = len(re.findall(r'[\u0600-\u06FF]', val_str))
                english_chars = len(re.findall(r'[a-zA-Z]', val_str))

                if arabic_chars > 0 and english_chars > 0:
                    # Check if it's inappropriate mixing (not technical terms)
                    if not any(tech in val_str.lower() for tech in ['http', 'www', 'email', '@', '.com', '.org']):
                        issues['data_entry_errors'].append({
                            'value': str(val),
                            'issue': 'Inappropriate language mixing'
                        })

        # 2. Obvious typos and errors
        common_errors = {
            'teh': 'the',
            'adn': 'and',
            'taht': 'that',
            'thsi': 'this',
            'wihch': 'which',
            'recieve': 'receive',
            'seperate': 'separate'
        }

        for val in str_data:
            val_lower = str(val).lower().strip()
            for error, correction in common_errors.items():
                if error in val_lower:
                    issues['data_entry_errors'].append({
                        'value': str(val),
                        'issue': f'Possible typo: "{error}" should be "{correction}"'
                    })

        # 3. Unusual capitalization
        for val in str_data:
            val_str = str(val).strip()
            if len(val_str) > 3 and val_str.isalpha():
                # All caps (might be data entry error)
                if val_str.isupper() and len(val_str) > 10:
                    issues['data_entry_errors'].append({
                        'value': str(val),
                        'issue': 'All caps text (possible data entry error)'
                    })

                # Mixed case inappropriately
                elif sum(1 for c in val_str if c.isupper()) > len(val_str) * 0.5:
                    issues['data_entry_errors'].append({
                        'value': str(val),
                        'issue': 'Inappropriate capitalization'
                    })

        # Remove duplicates from each category
        for category in issues:
            if isinstance(issues[category], list):
                seen = set()
                unique_issues = []
                for item in issues[category]:
                    if isinstance(item, dict):
                        key = item.get('value', str(item))
                    else:
                        key = str(item)

                    if key not in seen:
                        seen.add(key)
                        unique_issues.append(item)

                issues[category] = unique_issues[:20]  # Limit to 20 per category

        return issues

    except Exception as e:
        logger.error(f"Error in data quality issue detection for {col_name}: {e}")
        return issues

def detect_sector_from_data(df, filename=""):
    """
    Detect sector type from data analysis
    """
    try:
        sector_indicators = {
            'insurance': {
                'keywords': ['تأمين', 'insurance', 'policy', 'premium', 'claim', 'coverage', 'beneficiary'],
                'columns': ['policy_number', 'insurance_type', 'premium_amount', 'claim_amount', 'coverage_type'],
                'score': 0
            },
            'customs': {
                'keywords': ['جمارك', 'customs', 'duty', 'tariff', 'declaration', 'hs_code', 'import', 'export'],
                'columns': ['hs_code', 'declaration_number', 'duty_amount', 'origin_country', 'customs_value'],
                'score': 0
            },
            'healthcare': {
                'keywords': ['صحة', 'health', 'medical', 'patient', 'diagnosis', 'treatment', 'hospital'],
                'columns': ['patient_id', 'diagnosis_code', 'treatment_type', 'medical_record'],
                'score': 0
            },
            'finance': {
                'keywords': ['مالية', 'finance', 'bank', 'account', 'transaction', 'loan', 'credit'],
                'columns': ['account_number', 'transaction_amount', 'balance', 'loan_amount'],
                'score': 0
            },
            'education': {
                'keywords': ['تعليم', 'education', 'student', 'grade', 'course', 'university', 'school'],
                'columns': ['student_id', 'grade', 'course_code', 'enrollment_date'],
                'score': 0
            }
        }

        # Analyze column names
        column_names = ' '.join(df.columns.astype(str).str.lower())

        for sector, indicators in sector_indicators.items():
            # Check keywords in column names
            for keyword in indicators['keywords']:
                if keyword.lower() in column_names:
                    indicators['score'] += 2

            # Check specific column patterns
            for col_pattern in indicators['columns']:
                if any(col_pattern.lower() in col.lower() for col in df.columns.astype(str)):
                    indicators['score'] += 1

        # Find sector with highest score
        best_sector = max(sector_indicators.items(), key=lambda x: x[1]['score'])

        if best_sector[1]['score'] >= 2:  # Minimum threshold
            sector_names = {
                'insurance': 'قطاع التأمين',
                'customs': 'قطاع الجمارك',
                'healthcare': 'قطاع الرعاية الصحية',
                'finance': 'قطاع المالية',
                'education': 'قطاع التعليم'
            }
            return sector_names.get(best_sector[0], 'قطاع غير محدد')

        # Fallback: check filename
        filename_lower = filename.lower()
        if 'insurance' in filename_lower or 'تأمين' in filename_lower:
            return 'قطاع التأمين'
        elif 'customs' in filename_lower or 'جمارك' in filename_lower:
            return 'قطاع الجمارك'

        return 'قطاع متعدد الأغراض'

    except Exception as e:
        logger.warning(f"Error detecting sector: {e}")
        return 'قطاع غير محدد'

def get_file_summary_info(df, filename=""):
    """
    Generate comprehensive file summary information
    """
    try:
        file_info = {
            'filename': filename,
            'rows_count': len(df),
            'columns_count': len(df.columns),
            'file_size_mb': 0,  # Will be calculated when file is available
            'sector': detect_sector_from_data(df, filename),
            'data_types': {},
            'memory_usage_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
            'numeric_columns': 0,
            'text_columns': 0,
            'date_columns': 0,
            'null_percentage': (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        }

        # Analyze column data types
        for col in df.columns:
            dtype = str(df[col].dtype)
            if dtype in file_info['data_types']:
                file_info['data_types'][dtype] += 1
            else:
                file_info['data_types'][dtype] = 1

            # Categorize columns
            if pd.api.types.is_numeric_dtype(df[col]):
                file_info['numeric_columns'] += 1
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                file_info['date_columns'] += 1
            else:
                file_info['text_columns'] += 1

        # Calculate file size if filename is provided
        if filename:
            try:
                import os
                if os.path.exists(filename):
                    file_info['file_size_mb'] = os.path.getsize(filename) / (1024 * 1024)
                elif hasattr(df, '_file_path') and os.path.exists(df._file_path):
                    file_info['file_size_mb'] = os.path.getsize(df._file_path) / (1024 * 1024)
            except:
                pass

        return file_info

    except Exception as e:
        logger.error(f"Error generating file summary: {e}")
        return {
            'filename': filename,
            'rows_count': len(df) if hasattr(df, '__len__') else 0,
            'columns_count': len(df.columns) if hasattr(df, 'columns') else 0,
            'sector': 'خطأ في التحليل',
            'error': str(e)
        }

# Create Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-in-production'
app.config['JSON_AS_ASCII'] = False

# إعدادات Flask للعمل بدون إنترنت
app.config['OFFLINE_MODE'] = True
app.config['LOCAL_STORAGE'] = True
app.config['CACHE_TYPE'] = 'filesystem'
app.config['CACHE_DIR'] = 'cache'

# Initialize database manager first
db_config = {
    'DATABASE_TYPE': DATABASE_TYPE,
    'SQLITE_CONFIG': SQLITE_CONFIG,
    'max_connections': 10,
    'timeout': 30
}
db_manager = init_database_manager(db_config)

# Initialize other managers
try:
    cache_manager = get_cache_manager()
except:
    cache_manager = None

try:
    performance_optimizer = get_performance_optimizer()
except:
    performance_optimizer = None

try:
    sector_manager = SectorManager()
except:
    sector_manager = None

try:
    security_manager = SecurityManager({})
except:
    security_manager = None

# Initialize streaming processor for large file handling
streaming_processor = None
try:
    from memory_monitor import MemoryMonitor
    streaming_config = {
        'chunk_size': 50000,  # Default chunk size for processing
        'max_memory_mb': 1024,  # 1GB memory limit
        'temp_dir': 'temp_processing'
    }
    streaming_processor = StreamingDataProcessor(streaming_config)
    logger.info("[INIT] Streaming processor initialized for large file handling")
except Exception as e:
    streaming_processor = None
    logger.warning(f"[INIT] Failed to initialize streaming processor: {e}")
    logger.warning("[INIT] Large file processing will be limited")

# Initialize memory monitor
memory_monitor = None
try:
    from memory_monitor import MemoryMonitor
    memory_config = {
        'max_snapshots': 50,
        'alert_thresholds': {
            'memory_percent': 85.0,
            'cpu_percent': 90.0,
            'memory_mb': 1024,
        },
        'monitor_interval': 60,  # Monitor every minute
        'tracemalloc_enabled': False  # Enable for detailed memory tracking
    }
    memory_monitor = MemoryMonitor(memory_config)
    memory_monitor.start_monitoring()
    logger.info("[INIT] Memory monitor initialized and started")
except Exception as e:
    memory_monitor = None
    logger.warning(f"[INIT] Failed to initialize memory monitor: {e}")
    logger.warning("[INIT] Memory monitoring will be disabled")

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # In offline mode, auto-login as admin user
        if app.config.get('OFFLINE_MODE', False):
            if 'user_id' not in session:
                session['user_id'] = 1
                session['username'] = 'admin'
                session['role'] = 'admin'
                logger.info("[OFFLINE_MODE] Auto-logged in as admin user")
        elif 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    return redirect(url_for('login'))



@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Simple authentication (replace with proper auth)
        if username == 'admin' and password == 'admin':
            session['user_id'] = 1
            session['username'] = username
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials')

    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user info from session
    user_info = {
        'full_name': session.get('username', 'مستخدم'),
        'role': session.get('role', 'user')
    }

    # Get permissions based on role
    permissions = ['قراءة البيانات']
    if user_info['role'] == 'admin':
        permissions.extend(['كتابة البيانات', 'إدارة المستخدمين', 'النسخ الاحتياطي'])

    # Debug logging for template diagnosis
    import os
    template_path = os.path.join(app.root_path, 'templates', 'dashboard.html')
    logger.info(f"Looking for dashboard template at: {template_path}")
    logger.info(f"Template exists: {os.path.exists(template_path)}")
    logger.info(f"Templates directory contents: {os.listdir(os.path.join(app.root_path, 'templates'))}")

    from datetime import datetime
    return render_template('dashboard.html',
                          user=user_info,
                          permissions=permissions,
                          sectors=SUPPORTED_SECTORS,
                          config={'system_name': 'نظام تحليل البيانات التأمينية'},
                          now=datetime.now())

@app.route('/independent-review')
@login_required
def independent_review():
    """Independent Review Screen for Text Field Analysis"""
    # Get user info from session
    user_info = {
        'full_name': session.get('username', 'مستخدم'),
        'role': session.get('role', 'user')
    }

    # Get permissions based on role
    permissions = ['قراءة البيانات']
    if user_info['role'] == 'admin':
        permissions.extend(['كتابة البيانات', 'إدارة المستخدمين', 'النسخ الاحتياطي'])

    # Get current file information if available
    current_file_info = session.get('current_file_info', {})

    return render_template('independent_review.html',
                          user=user_info,
                          permissions=permissions,
                          config={'system_name': 'نظام تحليل البيانات التأمينية'},
                          current_file_info=current_file_info,
                          now=datetime.now())

@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Settings page for user preferences and system configuration"""
    # Get user info from session
    user_info = {
        'full_name': session.get('username', 'مستخدم'),
        'role': session.get('role', 'user')
    }

    # Get permissions based on role
    permissions = ['قراءة البيانات']
    if user_info['role'] == 'admin':
        permissions.extend(['كتابة البيانات', 'إدارة المستخدمين', 'النسخ الاحتياطي'])

    if request.method == 'POST':
        # Handle settings update
        try:
            # Get form data
            theme = request.form.get('theme', 'light')
            language = request.form.get('language', 'ar')
            show_charts = 'show_charts' in request.form
            show_tables = 'show_tables' in request.form
            auto_refresh = 'auto_refresh' in request.form
            export_format = request.form.get('export_format', 'excel')
            items_per_page = int(request.form.get('items_per_page', 25))

            # Get sector-specific settings
            customs_criteria = request.form.getlist('customs_criteria[]')
            customs_display = request.form.getlist('customs_display[]')
            insurance_criteria = request.form.getlist('insurance_criteria[]')
            insurance_display = request.form.getlist('insurance_display[]')

            # Get column settings
            customs_columns = request.form.getlist('customs_columns[]')
            insurance_columns = request.form.getlist('insurance_columns[]')
            general_columns = request.form.getlist('general_columns[]')

            # Get backup settings (admin only)
            auto_backup = 'auto_backup' in request.form if user_info['role'] == 'admin' else False
            backup_retention_days = int(request.form.get('backup_retention_days', 7)) if user_info['role'] == 'admin' else 7
            backup_retention_weeks = int(request.form.get('backup_retention_weeks', 4)) if user_info['role'] == 'admin' else 4

            # Get password change data
            current_password = request.form.get('current_password', '')
            new_password = request.form.get('new_password', '')
            confirm_password = request.form.get('confirm_password', '')

            # Validate password change
            password_changed = False
            if new_password:
                if not current_password:
                    flash('يرجى إدخال كلمة المرور الحالية', 'error')
                    return redirect(url_for('settings'))
                if new_password != confirm_password:
                    flash('كلمة المرور الجديدة غير متطابقة', 'error')
                    return redirect(url_for('settings'))
                # Here you would validate current password and update it
                # For now, just show success message
                password_changed = True
                flash('تم تغيير كلمة المرور بنجاح', 'success')

            # Save settings to database or session
            user_settings = {
                'theme': theme,
                'language': language,
                'show_charts': show_charts,
                'show_tables': show_tables,
                'auto_refresh': auto_refresh,
                'export_format': export_format,
                'items_per_page': items_per_page,
                'customs_criteria_list': customs_criteria,
                'customs_display_list': customs_display,
                'insurance_criteria_list': insurance_criteria,
                'insurance_display_list': insurance_display,
                'customs_columns_list': customs_columns,
                'insurance_columns_list': insurance_columns,
                'general_columns_list': general_columns,
                'auto_backup': auto_backup,
                'backup_retention_days': backup_retention_days,
                'backup_retention_weeks': backup_retention_weeks
            }

            # Store in session for now (in production, save to database)
            session['user_settings'] = user_settings

            flash('تم حفظ الإعدادات بنجاح', 'success')
            return redirect(url_for('settings'))

        except Exception as e:
            logger.error(f"Error saving settings: {e}")
            flash('حدث خطأ في حفظ الإعدادات', 'error')
            return redirect(url_for('settings'))

    # GET request - display settings page
    # Get current user settings from session or defaults
    user_settings = session.get('user_settings', {
        'theme': 'light',
        'language': 'ar',
        'show_charts': True,
        'show_tables': True,
        'auto_refresh': False,
        'export_format': 'excel',
        'items_per_page': 25,
        'customs_criteria_list': ['data_quality', 'customs_analysis', 'customs_legal', 'intelligent_prediction'],
        'customs_display_list': ['charts', 'tables', 'kpis', 'alerts'],
        'insurance_criteria_list': ['data_quality', 'functional_link', 'chronological_sequence', 'financial_data_match'],
        'insurance_display_list': ['charts', 'tables', 'kpis', 'alerts'],
        'customs_columns_list': ['hs_code', 'description', 'quantity', 'weight', 'value', 'origin', 'duty', 'date'],
        'insurance_columns_list': ['employee_name', 'national_id', 'birth_date', 'salary', 'contributions', 'service_years', 'sector', 'employer', 'status'],
        'general_columns_list': ['id', 'created_date', 'modified_date', 'status'],
        'auto_backup': True,
        'backup_retention_days': 7,
        'backup_retention_weeks': 4
    })

    return render_template('settings.html',
                          user=user_info,
                          user_settings=user_settings,
                          permissions=permissions,
                          config={'system_name': 'نظام تحليل البيانات التأمينية'},
                          now=datetime.now())

@app.route('/api/independent-review/preview', methods=['POST'])
def preview_file():
    """API endpoint to preview file columns"""
    try:
        logger.info("[PREVIEW] Starting file preview operation")

        file = request.files.get('file')
        logger.info(f"[PREVIEW] File received: {bool(file)}")

        if not file or not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            logger.error(f"[PREVIEW] Invalid file: file={bool(file)}, filename={file.filename if file else None}")
            return jsonify({'error': 'Invalid file'}), 400

        logger.info(f"[PREVIEW] Processing file: {file.filename}")

        # Simplified file reading with comprehensive error handling
        logger.info(f"[PREVIEW] Reading file: {file.filename}")

        try:
            # Reset file stream position
            file.stream.seek(0)

            if file.filename.lower().endswith('.csv'):
                logger.info("[PREVIEW] Reading CSV file")
                # Try different encodings for CSV files
                encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1256', 'iso-8859-6', 'windows-1256']
                df = None
                for encoding in encodings_to_try:
                    try:
                        file.stream.seek(0)
                        df = pd.read_csv(file.stream, encoding=encoding, nrows=1000)  # Limit rows for preview
                        logger.info(f"[PREVIEW] Successfully read CSV with encoding: {encoding}")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as enc_error:
                        logger.warning(f"[PREVIEW] Failed with encoding {encoding}: {enc_error}")
                        continue

                if df is None:
                    raise ValueError("Could not read CSV file with any supported encoding")

            else:
                logger.info("[PREVIEW] Reading Excel file")
                df = pd.read_excel(file.stream, engine='openpyxl', nrows=1000)  # Limit rows for preview

            logger.info(f"[PREVIEW] File loaded. Shape: {df.shape}")

            # Validate DataFrame
            if df.empty:
                return jsonify({'error': 'File is empty or contains no data'}), 400

            if len(df.columns) == 0:
                return jsonify({'error': 'File contains no columns'}), 400

            # Fix Arabic text inversion in column names
            logger.info("[PREVIEW] Processing Arabic column names")
            df = process_arabic_column_names(df)

            # Get the processed column names
            columns = df.columns.tolist()
            logger.info(f"[PREVIEW] Processed columns: {columns}")

            return jsonify({
                'columns': columns,
                'row_count': len(df),
                'filename': file.filename
            })

        except pd.errors.EmptyDataError:
            logger.error("[PREVIEW] File is empty")
            return jsonify({'error': 'File is empty'}), 400
        except pd.errors.ParserError as parse_error:
            logger.error(f"[PREVIEW] Parser error: {parse_error}")
            return jsonify({'error': f'File parsing error: {str(parse_error)}'}), 400
        except UnicodeDecodeError as decode_error:
            logger.error(f"[PREVIEW] Unicode decode error: {decode_error}")
            return jsonify({'error': 'File encoding not supported. Try saving as UTF-8 CSV or Excel format'}), 400
        except Exception as read_error:
            logger.error(f"[PREVIEW] Error reading file: {read_error}")
            logger.error(f"[PREVIEW] Error type: {type(read_error).__name__}")
            return jsonify({'error': f'Failed to read file: {str(read_error)}'}), 500

    except Exception as e:
        logger.error(f"[PREVIEW] Unexpected error in preview_file: {str(e)}")
        logger.error(f"[PREVIEW] Error type: {type(e).__name__}")
        import traceback
        logger.error(f"[PREVIEW] Traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Unexpected error: {str(e)}'}), 500

@app.route('/api/file-summary', methods=['POST'])
@login_required
def get_file_summary():
    """API endpoint to get comprehensive file summary information"""
    try:
        logger.info("[FILE_SUMMARY] Starting file summary operation")

        file = request.files.get('file')
        logger.info(f"[FILE_SUMMARY] File received: {bool(file)}")

        if not file or not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            logger.error(f"[FILE_SUMMARY] Invalid file: file={bool(file)}, filename={file.filename if file else None}")
            return jsonify({'error': 'Invalid file'}), 400

        logger.info(f"[FILE_SUMMARY] Processing file: {file.filename}")

        # Read file to get summary with comprehensive error handling
        try:
            # Reset file stream position
            file.stream.seek(0)

            if file.filename.lower().endswith('.csv'):
                logger.info("[FILE_SUMMARY] Reading CSV file")
                # Try different encodings for CSV files
                encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1256', 'iso-8859-6', 'windows-1256']
                df = None
                for encoding in encodings_to_try:
                    try:
                        file.stream.seek(0)
                        df = pd.read_csv(file.stream, encoding=encoding, nrows=50000)  # Limit rows for summary
                        logger.info(f"[FILE_SUMMARY] Successfully read CSV with encoding: {encoding}")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as enc_error:
                        logger.warning(f"[FILE_SUMMARY] Failed with encoding {encoding}: {enc_error}")
                        continue

                if df is None:
                    raise ValueError("Could not read CSV file with any supported encoding")

            else:
                logger.info("[FILE_SUMMARY] Reading Excel file")
                df = pd.read_excel(file.stream, engine='openpyxl', nrows=50000)  # Limit rows for summary

            logger.info(f"[FILE_SUMMARY] File loaded. Shape: {df.shape}")

            # Validate DataFrame
            if df.empty:
                return jsonify({'error': 'File is empty or contains no data'}), 400

            if len(df.columns) == 0:
                return jsonify({'error': 'File contains no columns'}), 400

            # Process Arabic text in column names
            logger.info("[FILE_SUMMARY] Processing Arabic column names")
            df = process_arabic_column_names(df)

            # Get comprehensive file summary with error handling
            try:
                file_summary = get_file_summary_info(df, file.filename)
            except Exception as summary_error:
                logger.error(f"[FILE_SUMMARY] Error generating file summary: {summary_error}")
                file_summary = {
                    'filename': file.filename,
                    'rows_count': len(df),
                    'columns_count': len(df.columns),
                    'sector': 'خطأ في التحليل',
                    'error': str(summary_error)
                }

            # Store file info in session for use in templates
            session['current_file_info'] = file_summary

            logger.info(f"[FILE_SUMMARY] Generated summary: {file_summary}")

            return jsonify({
                'file_summary': file_summary,
                'success': True
            })

        except pd.errors.EmptyDataError:
            logger.error("[FILE_SUMMARY] File is empty")
            return jsonify({'error': 'File is empty'}), 400
        except pd.errors.ParserError as parse_error:
            logger.error(f"[FILE_SUMMARY] Parser error: {parse_error}")
            return jsonify({'error': f'File parsing error: {str(parse_error)}'}), 400
        except UnicodeDecodeError as decode_error:
            logger.error(f"[FILE_SUMMARY] Unicode decode error: {decode_error}")
            return jsonify({'error': 'File encoding not supported. Try saving as UTF-8 CSV or Excel format'}), 400
        except MemoryError:
            logger.error("[FILE_SUMMARY] Memory error - file too large")
            return jsonify({'error': 'File is too large to process. Try a smaller file or contact administrator'}), 413
        except Exception as read_error:
            logger.error(f"[FILE_SUMMARY] Error reading file: {read_error}")
            logger.error(f"[FILE_SUMMARY] Error type: {type(read_error).__name__}")
            return jsonify({'error': f'Failed to read file: {str(read_error)}'}), 500

    except Exception as e:
        logger.error(f"[FILE_SUMMARY] Unexpected error in get_file_summary: {str(e)}")
        logger.error(f"[FILE_SUMMARY] Error type: {type(e).__name__}")
        import traceback
        logger.error(f"[FILE_SUMMARY] Traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Unexpected error: {str(e)}'}), 500
@app.route('/api/advanced/large-file-options', methods=['GET'])
@login_required
def get_large_file_options():
    """Get advanced options for large file processing"""
    options = {
        'chunk_sizes': {
            'small': 10000,
            'medium': 50000,
            'large': 100000,
            'xl': 500000
        },
        'memory_limits': {
            'low': 256,
            'medium': 512,
            'high': 1024,
            'xl': 2048
        },
        'processing_modes': {
            'streaming': 'معالجة متدفقة للملفات الكبيرة',
            'chunked': 'معالجة بدفعات',
            'sampled': 'معالجة عينة فقط',
            'aggregated': 'معالجة إحصائية مجمعة'
        },
        'compression_options': {
            'none': 'بدون ضغط',
            'gzip': 'ضغط gzip',
            'lz4': 'ضغط lz4 (أسرع)',
            'zstd': 'ضغط zstd (أفضل)'
        },
        'performance_profiles': {
            'speed': {
                'name': 'الأولوية للسرعة',
                'chunk_size': 100000,
                'memory_limit': 2048,
                'compression': 'lz4',
                'description': 'أسرع معالجة مع استخدام أعلى للذاكرة'
            },
            'memory': {
                'name': 'الأولوية للذاكرة',
                'chunk_size': 10000,
                'memory_limit': 256,
                'compression': 'zstd',
                'description': 'توفير في الذاكرة مع سرعة متوسطة'
            },
            'balanced': {
                'name': 'متوازن',
                'chunk_size': 50000,
                'memory_limit': 512,
                'compression': 'gzip',
                'description': 'توازن بين السرعة والذاكرة'
            },
            'conservative': {
                'name': 'محافظ',
                'chunk_size': 5000,
                'memory_limit': 128,
                'compression': 'none',
                'description': 'أبطأ لكن أكثر أماناً للذاكرة'
            }
        }
    }

    return jsonify({
        'large_file_options': options,
        'current_config': {
            'streaming_available': streaming_processor is not None,
            'memory_monitor_available': memory_monitor is not None,
            'max_file_size_mb': 2048,
            'recommended_chunk_size': 50000,
            'recommended_memory_limit': 1024
        }
    })

@app.route('/api/advanced/processing-config', methods=['POST'])
@login_required
def set_processing_config():
    """Set advanced processing configuration for large files"""
    try:
        config_data = request.get_json()

        # Validate configuration
        chunk_size = config_data.get('chunk_size', 50000)
        memory_limit = config_data.get('memory_limit', 1024)
        processing_mode = config_data.get('processing_mode', 'streaming')
        compression = config_data.get('compression', 'none')
        performance_profile = config_data.get('performance_profile')

        # Apply performance profile if specified
        if performance_profile and performance_profile in ['speed', 'memory', 'balanced', 'conservative']:
            profiles = {
                'speed': {'chunk_size': 100000, 'memory_limit': 2048, 'compression': 'lz4'},
                'memory': {'chunk_size': 10000, 'memory_limit': 256, 'compression': 'zstd'},
                'balanced': {'chunk_size': 50000, 'memory_limit': 512, 'compression': 'gzip'},
                'conservative': {'chunk_size': 5000, 'memory_limit': 128, 'compression': 'none'}
            }
            profile_config = profiles[performance_profile]
            chunk_size = profile_config['chunk_size']
            memory_limit = profile_config['memory_limit']
            compression = profile_config['compression']

        # Update streaming processor config
        if streaming_processor:
            streaming_processor.chunk_size = chunk_size
            streaming_processor.max_memory_mb = memory_limit
            streaming_processor.compression = compression

            logger.info(f"[CONFIG] Updated streaming processor: chunk_size={chunk_size}, memory_limit={memory_limit}MB, compression={compression}")

        # Update memory monitor config
        if memory_monitor:
            memory_monitor.config['alert_thresholds']['memory_mb'] = memory_limit
            logger.info(f"[CONFIG] Updated memory monitor: memory_limit={memory_limit}MB")

        return jsonify({
            'success': True,
            'message': 'تم تحديث إعدادات المعالجة',
            'config': {
                'chunk_size': chunk_size,
                'memory_limit': memory_limit,
                'processing_mode': processing_mode,
                'compression': compression,
                'performance_profile': performance_profile
            }
        })

    except Exception as e:
        logger.error(f"[CONFIG] Error setting processing config: {e}")
        return jsonify({'error': f'فشل في تحديث الإعدادات: {str(e)}'}), 500

@app.route('/api/test/independent-review/preview', methods=['POST'])
def test_preview_file():
    """Test API endpoint to preview file columns (no authentication required)"""
    try:
        logger.info("[TEST_PREVIEW] Starting file preview operation")
        file = request.files.get('file')
        if not file or not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            logger.error(f"[TEST_PREVIEW] Invalid file: file={bool(file)}, filename={file.filename if file else None}")
            return jsonify({'error': 'Invalid file'}), 400

        logger.info(f"[TEST_PREVIEW] Processing file: {file.filename}")

        # Read file to get columns
        if file.filename.lower().endswith('.csv'):
            logger.info("[TEST_PREVIEW] Reading CSV file")
            df = pd.read_csv(file.stream, encoding='utf-8')
        else:
            logger.info("[TEST_PREVIEW] Reading Excel file with openpyxl")
            df = pd.read_excel(file.stream, engine='openpyxl')

        logger.info(f"[TEST_PREVIEW] File loaded successfully. Shape: {df.shape}")

        # Process Arabic text in the DataFrame
        logger.info("[TEST_PREVIEW] Processing Arabic text in DataFrame")
        df = process_dataframe_arabic_text(df)

        # Fix Arabic text inversion in column names using centralized function
        logger.info("[TEST_PREVIEW] Processing Arabic column names")
        df = process_arabic_column_names(df)
        validate_dataframe_columns(df)

        columns = df.columns.tolist()
        logger.info(f"[TEST_PREVIEW] Found {len(columns)} columns: {columns}")

        return jsonify({
            'columns': columns,
            'row_count': len(df),
            'filename': file.filename
        })

    except Exception as e:
        logger.error(f"[TEST_PREVIEW] Error previewing file: {str(e)}")
        logger.error(f"[TEST_PREVIEW] Error type: {type(e).__name__}")
        import traceback
        logger.error(f"[TEST_PREVIEW] Traceback: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500



@app.route('/api/independent-review/analyze-numeric', methods=['POST'])
@login_required
def analyze_numeric_fields():
    """API endpoint for numeric field analysis with memory monitoring and support for large files"""
    # Start memory monitoring for this analysis
    if memory_monitor:
        initial_memory = memory_monitor.take_snapshot()
        logger.info(f"[MEMORY] Starting numeric analysis - Initial memory: {initial_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024):.1f} MB")
    try:
        # Get uploaded files or use sample data
        uploaded_files = request.files.getlist('files')
        field_settings = request.form.get('field_settings')

        if field_settings:
            try:
                field_settings = json.loads(field_settings)
            except:
                field_settings = {}
        else:
            field_settings = {}

        # Initialize streaming processor for large file handling
        streaming_config = {
            'chunk_size': 50000,  # Larger chunks for numeric analysis
            'max_memory_mb': 1024,  # 1GB memory limit
            'temp_dir': 'temp_processing'
        }
        streaming_processor = StreamingDataProcessor(streaming_config)

        if not uploaded_files:
            # Use sample data from uploads directory
            import os
            upload_dir = 'uploads'
            if os.path.exists(upload_dir):
                # Try to find Excel files first, but fall back to CSV if openpyxl is not available
                sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls'))]
                if not sample_files:
                    # If no Excel files, try CSV files
                    sample_files = [f for f in os.listdir(upload_dir) if f.endswith('.csv')]

                if sample_files:
                    file_path = os.path.join(upload_dir, sample_files[0])
                    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

                    try:
                        if file_size_mb > 100:  # Use streaming for files > 100MB
                            logger.info(f"[NUMERIC_ANALYSIS] Large file ({file_size_mb:.1f}MB) - using streaming processor")

                            # For numeric analysis, we need to aggregate statistics across chunks
                            numeric_stats = {
                                'total_rows': 0,
                                'columns': {},
                                'chunks_processed': 0
                            }

                            def analyze_numeric_chunk(chunk_df):
                                """Analyze numeric columns in a chunk"""
                                chunk_stats = {'rows': len(chunk_df), 'numeric_columns': {}}

                                # Process Arabic column names
                                chunk_df = process_arabic_column_names(chunk_df)

                                # Analyze numeric columns in this chunk
                                numeric_cols = chunk_df.select_dtypes(include=['int64', 'float64']).columns

                                for col in numeric_cols:
                                    col_data = chunk_df[col].dropna()
                                    if len(col_data) > 0:
                                        chunk_stats['numeric_columns'][col] = {
                                            'count': len(col_data),
                                            'sum': col_data.sum(),
                                            'sum_sq': (col_data ** 2).sum(),
                                            'min': col_data.min(),
                                            'max': col_data.max(),
                                            'zeros': (col_data == 0).sum(),
                                            'negatives': (col_data < 0).sum()
                                        }

                                return chunk_stats

                            result = streaming_processor.process_large_file(
                                file_path,
                                analyze_numeric_chunk,
                                output_path=None
                            )

                            if result['success']:
                                # Aggregate results from all chunks would be complex
                                # For now, load a sample for analysis
                                if file_path.endswith(('.xlsx', '.xls')):
                                    df = pd.read_excel(file_path, nrows=50000, engine='openpyxl')  # Sample 50k rows
                                else:
                                    df = pd.read_csv(file_path, nrows=50000, encoding='utf-8')
                                logger.info(f"[NUMERIC_ANALYSIS] Large file - loaded sample of {len(df)} rows for analysis")
                            else:
                                return jsonify({'error': f'Error processing large file: {result.get("error", "Unknown error")}'}), 400
                        else:
                            # Small file - load normally
                            if file_path.endswith(('.xlsx', '.xls')):
                                df = pd.read_excel(file_path, engine='openpyxl')
                            else:
                                df = pd.read_csv(file_path, encoding='utf-8')
                    except ImportError as e:
                        if 'openpyxl' in str(e):
                            return jsonify({'error': 'Missing optional dependency \'openpyxl\'. Use pip or conda to install openpyxl.'}), 400
                        else:
                            return jsonify({'error': f'Error reading file: {str(e)}'}), 400
                    except Exception as e:
                        return jsonify({'error': f'Error reading file: {str(e)}'}), 400
                else:
                    return jsonify({'error': 'No data files found'}), 400
            else:
                return jsonify({'error': 'No data available'}), 400
        else:
            # Process uploaded files
            file = uploaded_files[0]
            file_size = len(file.read())
            file.seek(0)  # Reset file pointer
            file_size_mb = file_size / (1024 * 1024)

            if file_size_mb > 5:  # Use streaming for files > 5MB
                logger.info(f"[NUMERIC_ANALYSIS] Large uploaded file ({file_size_mb:.1f}MB) - using streaming processor")

                # Save file temporarily for streaming processing
                import uuid
                import os
                temp_filename = f"temp_numeric_{uuid.uuid4()}_{file.filename}"
                temp_path = os.path.join('temp_processing', temp_filename)
                os.makedirs('temp_processing', exist_ok=True)

                with open(temp_path, 'wb') as temp_file:
                    file.save(temp_file)

                # For large files, aggregate numeric statistics
                numeric_aggregates = {}

                def aggregate_numeric_chunk(chunk_df):
                    """Aggregate numeric statistics from chunk"""
                    chunk_df = process_arabic_column_names(chunk_df)
                    numeric_cols = chunk_df.select_dtypes(include=['int64', 'float64']).columns

                    for col in numeric_cols:
                        if col not in numeric_aggregates:
                            numeric_aggregates[col] = {
                                'total_count': 0,
                                'sum': 0,
                                'sum_sq': 0,
                                'min': float('inf'),
                                'max': float('-inf'),
                                'zeros': 0,
                                'negatives': 0
                            }

                        col_data = chunk_df[col].dropna()
                        if len(col_data) > 0:
                            numeric_aggregates[col]['total_count'] += len(col_data)
                            numeric_aggregates[col]['sum'] += col_data.sum()
                            numeric_aggregates[col]['sum_sq'] += (col_data ** 2).sum()
                            numeric_aggregates[col]['min'] = min(numeric_aggregates[col]['min'], col_data.min())
                            numeric_aggregates[col]['max'] = max(numeric_aggregates[col]['max'], col_data.max())
                            numeric_aggregates[col]['zeros'] += (col_data == 0).sum()
                            numeric_aggregates[col]['negatives'] += (col_data < 0).sum()

                result = streaming_processor.process_large_file(
                    temp_path,
                    aggregate_numeric_chunk,
                    output_path=None
                )

                # Clean up temp file
                try:
                    os.remove(temp_path)
                except:
                    pass

                if result['success']:
                    # Create a sample dataframe for further processing
                    # Load first 10k rows for column analysis
                    file.seek(0)
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, nrows=10000, encoding='utf-8')
                    else:
                        df = pd.read_excel(file.stream, nrows=10000, engine='openpyxl')
                    logger.info(f"[NUMERIC_ANALYSIS] Large file processed with streaming - loaded sample of {len(df)} rows")
                else:
                    return jsonify({'error': f'Error processing large uploaded file: {result.get("error", "Unknown error")}'}), 400
            else:
                # Small file - process normally
                if file.filename.lower().endswith('.csv'):
                    df = pd.read_csv(file.stream, encoding='utf-8')
                else:
                    df = pd.read_excel(file.stream, engine='openpyxl')

        # Fix Arabic text inversion in column names using centralized function
        df = process_arabic_column_names(df)
        validate_dataframe_columns(df)


        # Analyze only numeric columns
        numeric_columns = df.select_dtypes(include=['int64', 'float64']).columns.tolist()
        logger.info(f"Found {len(numeric_columns)} numeric columns: {numeric_columns}")

        # For large files, we may have aggregated data from streaming processor
        if 'numeric_aggregates' in locals() and numeric_aggregates:
            logger.info(f"[NUMERIC_ANALYSIS] Using aggregated data from streaming processor for {len(numeric_aggregates)} columns")
            # Use aggregated statistics instead of full DataFrame analysis
            # This would require modifying the analysis logic below

        # DEBUG: Log suspicious field names and their sources
        suspicious_fields = ['replace', 'len', 'mid', 'val']
        for field in suspicious_fields:
            if field in df.columns:
                logger.warning(f"SUSPICIOUS FIELD DETECTED: '{field}' found in DataFrame columns (type: {df[field].dtype}). This looks like a data processing artifact.")
                # Check if this field has identical statistics to others
                field_data = df[field].dropna()
                if len(field_data) > 0:
                    logger.warning(f"Field '{field}' statistics: count={len(field_data)}, unique={len(field_data.unique())}, mean={field_data.mean():.2f}")
                else:
                    logger.warning(f"Field '{field}' is empty or all null values")
            else:
                logger.info(f"Suspicious field '{field}' NOT found in DataFrame columns")

        # Log all columns for debugging
        logger.info(f"Total DataFrame columns: {len(df.columns)}")
        logger.info(f"First 10 column names: {list(df.columns[:10])}")

        # قائمة الحقول الثابتة التي يجب استبعادها من التحليل
        static_fields_to_exclude = {
            'PortCode', 'PortName', 'DeclarationNumber', 'DeclarationType',
            'CountryCode', 'CurrencyCode', 'HSCode', 'ItemNumber',
            'SerialNumber', 'BatchNumber', 'ReferenceNumber'
        }

        # تخصيص إعدادات التحليل لكل عمود بناءً على نوع البيانات والاستخدام المتوقع
        column_settings = {
            # أعمدة الأسماء والمعلومات الشخصية
            'اسم': {
                'importance': 'عالية',
                'repetition_allowed': 'غير قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالهوية والعنوان',
                'show_in_table': True
            },
            'الاسم': {
                'importance': 'عالية',
                'repetition_allowed': 'غير قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالهوية والعنوان',
                'show_in_table': True
            },
            'اسم الأم': {
                'importance': 'عالية',
                'repetition_allowed': 'غير قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالهوية والعنوان',
                'show_in_table': True
            },
            'العنوان': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '2-4',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-\d]+$',
                'cross_field_rules': 'مرتبط بالهوية والمنطقة',
                'show_in_table': True
            },
            'عنوان': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '2-4',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-\d]+$',
                'cross_field_rules': 'مرتبط بالهوية والمنطقة',
                'show_in_table': True
            },
            'الجنسية': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s]+$',
                'cross_field_rules': 'مرتبط بالهوية',
                'show_in_table': True
            },
            'المحافظة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s]+$',
                'cross_field_rules': 'مرتبط بالعنوان',
                'show_in_table': True
            },
            'المدينة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s]+$',
                'cross_field_rules': 'مرتبط بالعنوان',
                'show_in_table': True
            },
            'المنطقة': {
                'importance': 'منخفضة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s]+$',
                'cross_field_rules': 'مرتبط بالعنوان',
                'show_in_table': True
            },
            'الوظيفة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالراتب والقسم',
                'show_in_table': True
            },
            'القسم': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s]+$',
                'cross_field_rules': 'مرتبط بالوظيفة والراتب',
                'show_in_table': True
            },
            'الشركة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالوظيفة',
                'show_in_table': True
            },
            'المؤسسة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير الموارد البشرية',
                'regex_pattern': r'^[\u0600-\u06FF\s\-]+$',
                'cross_field_rules': 'مرتبط بالوظيفة',
                'show_in_table': True
            },

            # أعمدة الفروع والمكاتب
            'الفرع': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],
                'data_source': 'نظامي',
                'data_steward': 'مدير الفروع',
                'regex_pattern': r'^[\u0600-\u06FF\s\-\d]+$',
                'cross_field_rules': 'مرتبط بالمكتب والمنطقة',
                'show_in_table': True
            },

            # أعمدة الأرقام المالية (المعايير القديمة)
            'القيمة': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': 'لا ينطبق',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات المالية',
                'regex_pattern': r'^\d+(\.\d{1,2})?$',
                'cross_field_rules': 'مرتبط بتاريخ الفاتورة والعميل',
                'show_in_table': True
            },
            'الكمية': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': 'لا ينطبق',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير المخزون',
                'regex_pattern': r'^\d+$',
                'cross_field_rules': 'مرتبطة بالمنتج والوحدة',
                'show_in_table': True
            },
            'السعر': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': 'لا ينطبق',
                'limited_values': [],
                'data_source': 'تكامل خارجي',
                'data_steward': 'مدير التسعير',
                'regex_pattern': r'^\d+(\.\d{1,2})?$',
                'cross_field_rules': 'مرتبط بالمنتج والتاريخ',
                'show_in_table': True
            }
        }

        numeric_analysis = []

        for col in numeric_columns:
            # استبعاد الحقول الثابتة من التحليل
            if col in static_fields_to_exclude:
                logger.info(f"Excluding static field '{col}' from analysis as requested")
                continue

            col_data = df[col].dropna()
            total_records = len(col_data)

            if total_records == 0:
                continue

            # البحث عن إعدادات العمود بناءً على الاسم أو الكلمات المفتاحية
            settings = {}
            col_lower = str(col).lower()

            # أولوية 1: التحقق من إعدادات المستخدم المخصصة أولاً
            if col in field_settings:
                settings = field_settings[col].copy()
                logger.info(f"Using user custom settings for field '{col}'")
            else:
                # أولوية 2: البحث في إعدادات الأعمدة المخصصة مع مطابقة أفضل
                for key, setting in column_settings.items():
                    key_lower = key.lower()
                    # مطابقة دقيقة أولاً
                    if key_lower == col_lower:
                        settings = setting.copy()
                        logger.info(f"Exact match found for field '{col}' with key '{key}'")
                        break
                    # مطابقة جزئية للكلمات الرئيسية
                    elif any(keyword in col_lower for keyword in key_lower.split()):
                        settings = setting.copy()
                        logger.info(f"Partial match found for field '{col}' with key '{key}'")
                        break

                # إذا لم يتم العثور على إعدادات، استخدم الإعدادات الافتراضية العامة
                if not settings:
                    settings = {}
                    logger.info(f"Using general default settings for field '{col}'")

            # التأكد من أن الإعدادات تحتوي على show_in_table
            if 'show_in_table' not in settings:
                settings['show_in_table'] = True

            # استخراج الإعدادات مع القيم الافتراضية
            importance = settings.get('importance', 'متوسطة')
            repetition_allowed = settings.get('repetition_allowed', 'قابلة للتكرار')
            sequence_required = settings.get('sequence_required', 'لا')
            expected_words = settings.get('expected_words', 'لا ينطبق')
            limited_values = settings.get('limited_values', [])
            data_source = settings.get('data_source', 'يدوي')
            data_steward = settings.get('data_steward', 'مدير الجودة')
            regex_pattern = settings.get('regex_pattern', 'غير محدد')
            cross_field_rules = settings.get('cross_field_rules', 'لا توجد قواعد متقاطعة محددة')
            show_in_table = settings.get('show_in_table', True)  # إظهار في الجدول افتراضياً

            # تخطي الحقول التي لا يجب إظهارها في الجدول
            if not show_in_table:
                logger.info(f"Skipping column '{col}' - hidden from table display per settings")
                continue

            # التأكد من أن الحقل له إعدادات في column_settings
            if col not in column_settings:
                column_settings[col] = {
                    'importance': importance,
                    'repetition_allowed': repetition_allowed,
                    'sequence_required': sequence_required,
                    'expected_words': expected_words,
                    'limited_values': limited_values,
                    'data_source': data_source,
                    'data_steward': data_steward,
                    'regex_pattern': regex_pattern,
                    'cross_field_rules': cross_field_rules,
                    'show_in_table': show_in_table
                }
                logger.info(f"Ensured settings exist for field '{col}'")

            # Basic statistics - include empty strings for text fields
            null_count = df[col].isnull().sum()
            if pd.api.types.is_string_dtype(df[col]) or df[col].dtype == 'object':
                # For text fields, count empty strings and whitespace-only strings as null
                null_count += (df[col] == '').sum()
                null_count += (df[col].astype(str).str.strip() == '').sum()
            zero_count = (df[col] == 0).sum()

            # Calculate statistics only for non-null values
            if len(col_data) > 0:
                mean_val = col_data.mean()
                median_val = col_data.median()
                std_dev = col_data.std()
                min_val = col_data.min()
                max_val = col_data.max()

                # Quartiles
                q25 = col_data.quantile(0.25)
                q75 = col_data.quantile(0.75)

                # Outlier detection using IQR method
                iqr = q75 - q25
                lower_bound = q25 - 1.5 * iqr
                upper_bound = q75 + 1.5 * iqr
                outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                outlier_count = len(outliers)

                # Quality score calculation based on settings
                quality_score = 100
    
                # Penalize for null values
                if null_count > 0:
                    null_penalty = min(30, (null_count / len(df)) * 100)
                    quality_score -= null_penalty
                    logger.info(f"[ANALYSIS] Field '{col}' quality reduced by {null_penalty:.1f} points for null values")
    
                # Penalize for limited values violations (only for text fields with limited values)
                if outlier_count > 0 and limited_values and len(limited_values) > 0:
                    outlier_penalty = min(20, (outlier_count / len(col_data)) * 50)
                    quality_score -= outlier_penalty
                    logger.info(f"[ANALYSIS] Field '{col}' quality reduced by {outlier_penalty:.1f} points for limited value violations")
    
                # Penalize for word count violations (only for text fields with expected words)
                # Note: word_violations is calculated later in the text analysis section
                pass  # Word violations are handled in text analysis section below
    
                # Penalize for high variability (only for numeric fields)
                if std_dev > mean_val * 2 and pd.api.types.is_numeric_dtype(col_data):
                    quality_score -= 10
                    logger.info(f"[ANALYSIS] Field '{col}' quality reduced by 10 points for high variability")

                # Additional validation for numeric fields
                if pd.api.types.is_numeric_dtype(col_data):
                    # Check for unrealistic values (like negative ages, etc.)
                    if 'age' in str(col).lower() or 'سن' in str(col):
                        invalid_ages = col_data[(col_data < 0) | (col_data > 150)].count()
                        if invalid_ages > 0:
                            quality_score -= min(15, invalid_ages * 2)
                            logger.info(f"[ANALYSIS] Field '{col}' quality reduced by {min(15, invalid_ages * 2)} points for invalid ages")

                    # Check for percentage fields
                    if 'percent' in str(col).lower() or '%' in str(col) or 'نسبة' in str(col):
                        invalid_percentages = col_data[(col_data < 0) | (col_data > 100)].count()
                        if invalid_percentages > 0:
                            quality_score -= min(15, invalid_percentages * 2)
                            logger.info(f"[ANALYSIS] Field '{col}' quality reduced by {min(15, invalid_percentages * 2)} points for invalid percentages")
    
                quality_score = max(0, min(100, quality_score))
                logger.info(f"[ANALYSIS] Field '{col}' final quality score: {quality_score:.1f}")

                # Quality level
                if quality_score >= 80:
                    quality_level = 'ممتازة'
                    quality_color = 'success'
                elif quality_score >= 60:
                    quality_level = 'جيدة'
                    quality_color = 'info'
                elif quality_score >= 40:
                    quality_level = 'متوسطة'
                    quality_color = 'warning'
                else:
                    quality_level = 'ضعيفة'
                    quality_color = 'danger'

                # Recommendations
                recommendations = []
                if null_count > 0:
                    recommendations.append('يُنصح بتعبئة القيم الناقصة')
                if outlier_count > 0:
                    recommendations.append('يُنصح بمراجعة القيم الشاذة')
                if std_dev > mean_val * 2:
                    recommendations.append('القيم متنوعة جداً - قد تحتاج لمراجعة')
                if len(col_data.unique()) == 1:
                    recommendations.append('جميع القيم متطابقة - تحقق من صحة البيانات')

                if not recommendations:
                    recommendations.append('البيانات تبدو جيدة')

                # Calculate duplicate percentage for numeric fields with safety checks
                duplicate_count = max(0, len(col_data) - len(col_data.unique())) if len(col_data) > 0 else 0
                duplicate_percentage = min(100.0, max(0.0, (duplicate_count / len(col_data) * 100))) if len(col_data) > 0 else 0

                # Calculate missing percentage correctly for numeric fields with safety checks
                total_rows = max(1, len(df))
                missing_count = max(0, null_count)
                missing_percentage = min(100.0, max(0.0, (missing_count / total_rows * 100))) if total_rows > 0 else 0

                # Enhanced outlier detection for numeric fields with safety checks
                numeric_outliers = detect_numeric_outliers(col_data, col)
                outlier_count = max(0, len(numeric_outliers))
                outlier_percentage = min(100.0, max(0.0, (outlier_count / len(col_data) * 100))) if len(col_data) > 0 else 0
                outlier_examples = numeric_outliers[:5]  # First 5 examples
        
                logger.info(f"[NUMERIC_ANALYSIS] Field '{col}': outliers={outlier_count}, percentage={outlier_percentage:.1f}%, examples={outlier_examples[:3]}")

                # Calculate quality score based on various factors
                quality_score = 100
                quality_issues = []

                # Reduce score for missing values
                if missing_percentage > 10:
                    quality_score -= min(30, missing_percentage)
                    quality_issues.append(f"قيم ناقصة: {missing_percentage:.1f}%")

                # Reduce score for high outlier percentage
                outlier_pct = (outlier_count / len(col_data)) * 100 if len(col_data) > 0 else 0
                if outlier_pct > 5:
                    quality_score -= min(20, outlier_pct)
                    quality_issues.append(f"قيم شاذة: {outlier_pct:.1f}%")

                # Reduce score for high zero values (if inappropriate)
                zero_pct = (zero_count / len(col_data)) * 100 if len(col_data) > 0 else 0
                if zero_pct > 20 and 'amount' in str(col).lower():
                    quality_score -= min(15, zero_pct)
                    quality_issues.append(f"قيم صفرية: {zero_pct:.1f}%")

                # Determine quality level and color
                if quality_score >= 80:
                    quality_level = "ممتاز"
                    quality_color = "success"
                elif quality_score >= 60:
                    quality_level = "جيد"
                    quality_color = "primary"
                elif quality_score >= 40:
                    quality_level = "متوسط"
                    quality_color = "warning"
                else:
                    quality_level = "ضعيف"
                    quality_color = "danger"

                # Generate recommendations
                recommendations = []
                if missing_percentage > 10:
                    recommendations.append("تحسين جودة البيانات")
                if outlier_pct > 5:
                    recommendations.append("مراجعة القيم الشاذة")
                if zero_pct > 20:
                    recommendations.append("التحقق من القيم الصفرية")

                recommendations_text = "، ".join(recommendations) if recommendations else "البيانات جيدة"

                numeric_analysis.append({
                    'field_name': str(col),
                    'field_type': 'رقمي',
                    'total_records': int(total_records),
                    'null_values': int(null_count),
                    'missing_percentage': round(float(missing_percentage), 2),
                    'zero_values': int(zero_count),
                    'min_value': float(min_val) if not pd.isna(min_val) else None,
                    'max_value': float(max_val) if not pd.isna(max_val) else None,
                    'mean': float(mean_val) if not pd.isna(mean_val) else None,
                    'median': float(median_val) if not pd.isna(median_val) else None,
                    'std_dev': float(std_dev) if not pd.isna(std_dev) else None,
                    'q25': float(q25) if not pd.isna(q25) else None,
                    'q75': float(q75) if not pd.isna(q75) else None,
                    'outlier_count': int(outlier_count),
                    'outlier_percentage': round(float(min(100.0, max(0.0, (outlier_count / len(col_data)) * 100))) if len(col_data) > 0 else 0, 2),
                    'unique_values': int(len(col_data.unique())),
                    'quality_score': int(max(0, quality_score)),
                    'quality_level': quality_level,
                    'quality_color': quality_color,
                    'data_source': 'ملف البيانات',
                    'recommendations': recommendations_text,
                    'duplicates': int(len(col_data) - len(col_data.unique())),
                    'duplicate_percentage': round(float(min(100.0, max(0.0, duplicate_percentage))), 2),
                    'quality_score': int(quality_score),
                    'quality_level': quality_level,
                    'quality_color': quality_color,
                    'data_source': 'قاعدة البيانات',
                    'recommendations': ', '.join(recommendations)
                })

        # Quality metrics for numeric fields (standardized with text analysis metrics)
        quality_metrics = {
            'total_numeric_fields': len(numeric_analysis),
            'high_quality_fields': sum(1 for f in numeric_analysis if f['quality_score'] >= 80),
            'medium_quality_fields': sum(1 for f in numeric_analysis if 60 <= f['quality_score'] < 80),
            'low_quality_fields': sum(1 for f in numeric_analysis if 40 <= f['quality_score'] < 60),
            'fields_with_nulls': sum(1 for f in numeric_analysis if f['null_values'] > 0),
            'fields_with_outliers': sum(1 for f in numeric_analysis if f['outlier_count'] > 0),
            'fields_with_duplicates': sum(1 for f in numeric_analysis if f['unique_values'] < f['total_records']),
            'average_quality_score': sum(f['quality_score'] for f in numeric_analysis) / len(numeric_analysis) if numeric_analysis else 0,
            # Standardized metrics for template compatibility
            'irregular_repetition': 0,  # Not applicable for numeric fields
            'input_gaps': sum(f['null_values'] for f in numeric_analysis if f['null_values'] > 0),  # Only count fields with actual nulls
            'word_gaps': 0,  # Not applicable for numeric fields
            'length_irregularities': 0,  # Not applicable for numeric fields
            'sequence_irregularities': 0,  # Not applicable for numeric fields
            'outlier_values': sum(f['outlier_count'] for f in numeric_analysis),
            'total': len(numeric_analysis),
            'total_records': len(df)  # Add total records for percentage calculations
        }

        # Ensure input_gaps is properly calculated and not zero
        if quality_metrics['input_gaps'] == 0 and quality_metrics['fields_with_nulls'] > 0:
            quality_metrics['input_gaps'] = sum(f['null_values'] for f in numeric_analysis if f['null_values'] > 0)

        # Add performance metrics for large file processing
        performance_info = {}
        if streaming_processor:
            performance_info = {
                'streaming_processor_available': True,
                'memory_usage_mb': streaming_processor._get_memory_usage(),
                'processing_method': 'streaming' if 'numeric_aggregates' in locals() else 'standard'
            }
        else:
            performance_info = {
                'streaming_processor_available': False,
                'processing_method': 'standard'
            }

        logger.info(f"Returning {len(numeric_analysis)} analyzed numeric fields")

        # Add memory monitoring results
        if memory_monitor:
            final_memory = memory_monitor.take_snapshot()
            memory_analysis = memory_monitor.get_memory_analysis()

            memory_usage = {
                'initial_mb': initial_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024),
                'final_mb': final_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024),
                'peak_mb': memory_analysis.get('tracemalloc_stats', {}).get('peak_mb', 0) if memory_analysis else 0,
                'gc_collections': memory_analysis.get('gc_stats', {}).get('collections', [0, 0, 0]) if memory_analysis else [0, 0, 0]
            }

            logger.info(f"[MEMORY] Numeric analysis completed - Memory usage: {memory_usage['initial_mb']:.1f}MB -> {memory_usage['final_mb']:.1f}MB (peak: {memory_usage['peak_mb']:.1f}MB)")

            # === التعلم التراكمي من البيانات الرقمية المحللة ===
            if AI_LEARNING_AVAILABLE:
                try:
                    ai_engine = get_safe_ai_learning_engine()
                    if ai_engine:
                        # تجميع نتائج التحليل الرقمي للتعلم
                        numeric_analysis_for_learning = {}
                        for result in numeric_analysis:
                            field_name = result.get('field_name')
                            if field_name:
                                numeric_analysis_for_learning[field_name] = {
                                    'outlier_count': result.get('outlier_count', 0),
                                    'outlier_examples': result.get('outlier_examples', []),
                                    'field_type': 'numeric',
                                    'statistics': result.get('statistics', {}),
                                    'quality_score': result.get('quality_score', 0)
                                }

                        # تطبيق التعلم التراكمي
                        learning_result = ai_engine.learn_from_data(df, numeric_analysis_for_learning, {})

                        if learning_result.get('success'):
                            logger.info(f"[AI_LEARNING] تم التعلم التراكمي للبيانات الرقمية: {learning_result['new_patterns_found']} نمط جديد")
                            learning_info = {
                                'ai_learning_applied': True,
                                'new_patterns_discovered': learning_result['new_patterns_found'],
                                'total_knowledge_base_size': learning_result['total_knowledge_base_size'],
                                'learning_processing_time': learning_result['processing_time']
                            }
                        else:
                            learning_info = {
                                'ai_learning_applied': False,
                                'learning_error': learning_result.get('error', 'خطأ غير معروف')
                            }
                    else:
                        learning_info = {
                            'ai_learning_applied': False,
                            'learning_error': 'فشل في الحصول على محرك التعلم الذكي'
                        }

                except Exception as e:
                    logger.error(f"[AI_LEARNING] خطأ في التعلم التراكمي للبيانات الرقمية: {e}")
                    learning_info = {
                        'ai_learning_applied': False,
                        'learning_error': str(e)
                    }
            else:
                learning_info = {
                    'ai_learning_applied': False,
                    'learning_error': 'محرك التعلم الذكي غير متوفر'
                }

            return jsonify({
                'numeric_analysis': numeric_analysis,
                'quality_metrics': quality_metrics,
                'total_numeric_fields': len(numeric_analysis),
                'performance_info': performance_info,
                'memory_usage': memory_usage,
                'ai_learning_info': learning_info
            })
        else:
            # === التعلم التراكمي من البيانات الرقمية المحللة (بدون مراقبة الذاكرة) ===
            if AI_LEARNING_AVAILABLE:
                try:
                    ai_engine = get_safe_ai_learning_engine()
                    if ai_engine:
                        # تجميع نتائج التحليل الرقمي للتعلم
                        numeric_analysis_for_learning = {}
                        for result in numeric_analysis:
                            field_name = result.get('field_name')
                            if field_name:
                                numeric_analysis_for_learning[field_name] = {
                                    'outlier_count': result.get('outlier_count', 0),
                                    'outlier_examples': result.get('outlier_examples', []),
                                    'field_type': 'numeric',
                                    'statistics': result.get('statistics', {}),
                                    'quality_score': result.get('quality_score', 0)
                                }

                        # تطبيق التعلم التراكمي
                        learning_result = ai_engine.learn_from_data(df, numeric_analysis_for_learning, {})

                        if learning_result.get('success'):
                            logger.info(f"[AI_LEARNING] تم التعلم التراكمي للبيانات الرقمية: {learning_result['new_patterns_found']} نمط جديد")
                            learning_info = {
                                'ai_learning_applied': True,
                                'new_patterns_discovered': learning_result['new_patterns_found'],
                                'total_knowledge_base_size': learning_result['total_knowledge_base_size']
                            }
                        else:
                            learning_info = {
                                'ai_learning_applied': False,
                                'learning_error': learning_result.get('error', 'خطأ غير معروف')
                            }
                    else:
                        learning_info = {
                            'ai_learning_applied': False,
                            'learning_error': 'فشل في الحصول على محرك التعلم الذكي'
                        }

                except Exception as e:
                    logger.error(f"[AI_LEARNING] خطأ في التعلم التراكمي للبيانات الرقمية: {e}")
                    learning_info = {
                        'ai_learning_applied': False,
                        'learning_error': str(e)
                    }
            else:
                learning_info = {
                    'ai_learning_applied': False,
                    'learning_error': 'محرك التعلم الذكي غير متوفر'
                }

            return jsonify({
                'success': True,
                'numeric_analysis': numeric_analysis,
                'quality_metrics': quality_metrics,
                'total_numeric_fields': len(numeric_analysis),
                'performance_info': performance_info,
                'ai_learning_info': learning_info
            })

    except Exception as e:
        logger.error(f"Error in numeric field analysis: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/independent-review/analyze', methods=['POST'])
def analyze_text_fields():
    """API endpoint for text field analysis with memory monitoring"""
    # Start memory monitoring for this analysis
    if memory_monitor:
        initial_memory = memory_monitor.take_snapshot()
        logger.info(f"[MEMORY] Starting text analysis - Initial memory: {initial_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024):.1f} MB")
    """API endpoint for text field analysis with support for large files"""
    try:
        logger.info("[ANALYSIS] ===== STARTING TEXT FIELD ANALYSIS =====")
        logger.info("[ANALYSIS] Request method: %s", request.method)
        logger.info("[ANALYSIS] Request content type: %s", request.content_type)
        logger.info("[ANALYSIS] Request headers: %s", dict(request.headers))
        logger.info("[ANALYSIS] Request form keys: %s", list(request.form.keys()) if request.form else 'None')
        logger.info("[ANALYSIS] Request files keys: %s", list(request.files.keys()) if request.files else 'None')

        # Get uploaded files or use sample data
        uploaded_files = request.files.getlist('files')
        field_settings = request.form.get('field_settings')

        logger.info(f"[ANALYSIS] ===== UPLOADED FILES ANALYSIS =====")
        logger.info(f"[ANALYSIS] Number of uploaded files: {len(uploaded_files)}")
        for i, file in enumerate(uploaded_files):
            logger.info(f"[ANALYSIS] File {i+1}: name={file.filename}, content_type={file.content_type}, size={len(file.read()) if hasattr(file, 'read') else 'unknown'}")
            if hasattr(file, 'seek'): file.seek(0)  # Reset file pointer
        logger.info(f"[ANALYSIS] Field settings received: {bool(field_settings)}")
        logger.info(f"[ANALYSIS] Field settings length: {len(field_settings) if field_settings else 0}")

        if field_settings:
            try:
                field_settings = json.loads(field_settings)
                logger.info(f"[ANALYSIS] Field settings parsed successfully: {len(field_settings)} fields")
            except Exception as e:
                logger.error(f"[ANALYSIS] Error parsing field settings: {e}")
                field_settings = {}
        else:
            field_settings = {}
            logger.info("[ANALYSIS] No field settings provided")

        # Initialize streaming processor for large file handling
        streaming_config = {
            'chunk_size': 50000,  # Larger chunks for better performance
            'max_memory_mb': 1024,  # 1GB memory limit
            'temp_dir': 'temp_processing'
        }
        streaming_processor = StreamingDataProcessor(streaming_config)

        if not uploaded_files:
            # Use sample data from uploads directory
            import os
            upload_dir = 'uploads'
            logger.info(f"[ANALYSIS] No uploaded files, checking uploads directory: {upload_dir}")
            logger.info(f"[ANALYSIS] Current working directory: {os.getcwd()}")
            logger.info(f"[ANALYSIS] Uploads directory exists: {os.path.exists(upload_dir)}")
            if os.path.exists(upload_dir):
                try:
                    sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))][:1]  # Use first file
                    logger.info(f"[ANALYSIS] Found sample files: {sample_files}")
                    if sample_files:
                        file_path = os.path.join(upload_dir, sample_files[0])
                        logger.info(f"[ANALYSIS] Using sample file: {file_path}")
                        logger.info(f"[ANALYSIS] File exists: {os.path.exists(file_path)}")

                        # Use streaming processor for large files
                        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        if file_size_mb > 100:  # Use streaming for files > 100MB
                            logger.info(f"[ANALYSIS] File size {file_size_mb:.1f}MB - using streaming processor")

                            def analyze_chunk(chunk_df):
                                """Process a chunk of data for analysis"""
                                # Process Arabic text in the chunk
                                chunk_df = process_dataframe_arabic_text(chunk_df)
                                return chunk_df

                            result = streaming_processor.process_large_file(
                                file_path,
                                analyze_chunk,
                                output_path=None
                            )

                            if result['success']:
                                # For large files, we need to aggregate results from chunks
                                # For now, load a sample for analysis
                                if file_path.endswith('.csv'):
                                    df = pd.read_csv(file_path, nrows=10000)  # Sample first 10k rows
                                else:
                                    df = pd.read_excel(file_path, nrows=10000, engine='openpyxl')
                                logger.info(f"[ANALYSIS] Large file - loaded sample of {len(df)} rows for analysis")
                            else:
                                return jsonify({'error': f'Error processing large file: {result.get("error", "Unknown error")}'}), 400
                        else:
                            # Small file - load normally
                            if file_path.endswith('.csv'):
                                df = pd.read_csv(file_path, encoding='utf-8')
                            else:
                                df = pd.read_excel(file_path, engine='openpyxl')
                            logger.info(f"[ANALYSIS] Small file loaded successfully. Shape: {df.shape}")
                    else:
                        logger.error("[ANALYSIS] No sample files found")
                        return jsonify({'error': 'No data files found'}), 400
                except Exception as file_error:
                    logger.error(f"[ANALYSIS] Error reading sample file: {file_error}")
                    logger.error(f"[ANALYSIS] File path attempted: {file_path}")
                    return jsonify({'error': f'Error reading sample file: {str(file_error)}'}), 400
            else:
                logger.error("[ANALYSIS] Uploads directory does not exist")
                logger.error(f"[ANALYSIS] Attempted path: {os.path.abspath(upload_dir)}")
                return jsonify({'error': 'No data available'}), 400
        else:
            # Process uploaded files
            logger.info(f"[ANALYSIS] ===== PROCESSING UPLOADED FILE =====")
            logger.info(f"[ANALYSIS] Processing uploaded file: {uploaded_files[0].filename}")
            logger.info(f"[ANALYSIS] File object type: {type(uploaded_files[0])}")
            logger.info(f"[ANALYSIS] File has stream attribute: {hasattr(uploaded_files[0], 'stream')}")
            try:
                file = uploaded_files[0]

                # Check file size for streaming decision
                file.seek(0, 2)  # Seek to end
                file_size = file.tell()
                file.seek(0)  # Reset to beginning
                file_size_mb = file_size / (1024 * 1024)

                if file_size_mb > 5:  # Use streaming for files > 5MB
                    logger.info(f"[ANALYSIS] Large uploaded file ({file_size_mb:.1f}MB) - using streaming processor")

                    # Save file temporarily for streaming processing
                    import uuid
                    import os
                    temp_filename = f"temp_analysis_{uuid.uuid4()}_{file.filename}"
                    temp_path = os.path.join('temp_processing', temp_filename)
                    os.makedirs('temp_processing', exist_ok=True)
    
                    with open(temp_path, 'wb') as temp_file:
                        file.save(temp_file)

                    def analyze_chunk(chunk_df):
                        """Process a chunk of data for analysis"""
                        # Process Arabic text in the chunk
                        chunk_df = process_dataframe_arabic_text(chunk_df)
                        return chunk_df

                    result = streaming_processor.process_large_file(
                        temp_path,
                        analyze_chunk,
                        output_path=None
                    )

                    # Clean up temp file
                    try:
                        os.remove(temp_path)
                    except:
                        pass

                    if result['success']:
                        # Load a sample for detailed analysis
                        import io
                        if file.filename.lower().endswith('.csv'):
                            df = pd.read_csv(io.BytesIO(file.read()), nrows=10000)
                        else:
                            df = pd.read_excel(io.BytesIO(file.read()), nrows=10000, engine='openpyxl')
                        file.seek(0)  # Reset for potential re-read
                        logger.info(f"[ANALYSIS] Large uploaded file - loaded sample of {len(df)} rows for analysis")
                    else:
                        return jsonify({'error': f'Error processing large uploaded file: {result.get("error", "Unknown error")}'}), 400
                else:
                    # Small file - process normally
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    logger.info(f"[ANALYSIS] Small uploaded file loaded successfully. Shape: {df.shape}")

            except Exception as upload_error:
                logger.error(f"[ANALYSIS] Error reading uploaded file: {upload_error}")
                logger.error(f"[ANALYSIS] Error type: {type(upload_error)}")
                return jsonify({'error': f'Error reading uploaded file: {str(upload_error)}'}), 400

            # Process Arabic text in the DataFrame
            logger.info("[ANALYSIS] Starting Arabic text processing for uploaded file")
            df = process_dataframe_arabic_text(df)

            # Validate Arabic text processing
            logger.info("[ANALYSIS] Validating Arabic text processing results")
            validation_issues = []
            for col in df.columns:
                if df[col].dtype == 'object':
                    sample_values = df[col].dropna().head(3)
                    for val in sample_values:
                        if not validate_arabic_text_processing(str(val)):
                            issue = f"Arabic text processing validation failed for column '{col}', value: '{val}'"
                            logger.warning(f"[ANALYSIS] {issue}")
                            validation_issues.append(issue)

            if validation_issues:
                logger.warning(f"[ANALYSIS] Found {len(validation_issues)} Arabic text processing validation issues")
                for issue in validation_issues[:5]:  # Log first 5 issues
                    logger.warning(f"[ANALYSIS] Issue: {issue}")
            else:
                logger.info("[ANALYSIS] Arabic text processing validation passed")

            logger.info(f"[ANALYSIS] Uploaded file processed. Shape: {df.shape}")

        # Fix Arabic text inversion in column names using centralized function
        logger.info("[ANALYSIS] Processing Arabic column names")
        df = process_arabic_column_names(df)
        validate_dataframe_columns(df)
        logger.info(f"[ANALYSIS] Column names after processing: {list(df.columns)}")



        # Analyze text fields using sector manager for field detection
        text_columns = df.select_dtypes(include=['object']).columns.tolist()
        logger.info(f"Found {len(text_columns)} text columns in Excel file: {text_columns}")

        # Also include numeric columns that might be codes (like HS codes)
        numeric_columns = df.select_dtypes(include=['int64', 'float64']).columns.tolist()
        logger.info(f"Found {len(numeric_columns)} numeric columns: {numeric_columns}")

        # Filter numeric columns that might be codes (length <= 10 digits)
        code_columns = []
        for col in numeric_columns:
            if df[col].notna().any():
                sample_values = df[col].dropna().head(5)
                # Check if values look like codes (no decimals, reasonable length)
                if all(isinstance(x, (int, float)) and float(x).is_integer() and len(str(int(x))) <= 10 for x in sample_values):
                    code_columns.append(col)

        # Include ALL columns for analysis (text + numeric codes)
        all_columns_to_analyze = list(df.columns)  # Include all columns
        logger.info(f"Total columns in file: {len(df.columns)}")
        logger.info(f"Text columns to analyze: {len(text_columns)}")
        logger.info(f"Code columns to analyze: {len(code_columns)}")
        logger.info(f"Total columns to analyze: {len(all_columns_to_analyze)}")

        # Performance optimization: Pre-calculate column statistics for faster processing
        logger.info("[PERFORMANCE] Pre-calculating column statistics for faster analysis...")
        column_stats_cache = {}
        for col in all_columns_to_analyze:
            try:
                col_data = df[col].dropna()
                column_stats_cache[col] = {
                    'total_count': len(col_data),
                    'unique_count': len(col_data.unique()),
                    'null_count': df[col].isnull().sum(),
                    'dtype': str(df[col].dtype),
                    'sample_values': col_data.head(10).tolist() if len(col_data) > 0 else []
                }
            except Exception as e:
                logger.warning(f"[PERFORMANCE] Error pre-calculating stats for column {col}: {e}")
                column_stats_cache[col] = {'error': str(e)}

        logger.info(f"[PERFORMANCE] Pre-calculated statistics for {len(column_stats_cache)} columns")

        # Enhanced comprehensive data quality analysis for all fields
        outlier_summary = {}
        data_quality_summary = {}
        logger.info(f"[DATA_QUALITY] Starting comprehensive data quality analysis for {len(all_columns_to_analyze)} fields")

        for col in all_columns_to_analyze:
            try:
                # Get all data including nulls for total count
                total_data = df[col]
                col_data = df[col].dropna()

                if len(col_data) == 0:
                    outlier_summary[col] = {
                        'outlier_count': 0,
                        'outlier_percentage': 0,
                        'total_values': len(total_data),
                        'outlier_examples': [],
                        'field_type': 'empty',
                        'data_quality_issues': {}
                    }
                    continue

                # Get field settings for this column
                field_settings = column_settings.get(col, {})

                # === COMPREHENSIVE OUTLIER DETECTION ===
                outliers = detect_outliers_comprehensive(col_data, col, field_settings)

                # === DATA QUALITY ISSUE DETECTION ===
                quality_issues = detect_data_quality_issues(col_data, col)

                # Determine field type
                field_type = 'numeric' if pd.api.types.is_numeric_dtype(df[col]) else 'text'

                # === ADDITIONAL ADVANCED CHECKS ===

                # 1. Pattern consistency analysis
                pattern_inconsistencies = 0
                if field_type == 'text' and len(col_data) > 10:
                    str_data = col_data.astype(str)
                    # Check for format inconsistencies
                    formats = {}
                    for val in str_data:
                        val_clean = str(val).strip()
                        if val_clean:
                            # Create format signature
                            format_sig = ''
                            for char in val_clean:
                                if char.isdigit():
                                    format_sig += 'D'
                                elif char.isalpha():
                                    format_sig += 'L'
                                elif char.isspace():
                                    format_sig += 'S'
                                else:
                                    format_sig += 'X'
                            formats[format_sig] = formats.get(format_sig, 0) + 1

                    # Count inconsistent formats
                    if len(formats) > 1:
                        total_formatted = sum(formats.values())
                        for fmt, count in formats.items():
                            if count / total_formatted < 0.1:  # Less than 10%
                                pattern_inconsistencies += count

                # 2. Data completeness analysis
                completeness_score = (len(col_data) / len(total_data)) * 100 if len(total_data) > 0 else 0

                # 3. Data uniqueness analysis
                uniqueness_score = (len(col_data.unique()) / len(col_data)) * 100 if len(col_data) > 0 else 0

                # 4. Data validity analysis (domain-specific)
                validity_issues = 0
                col_lower = str(col).lower()

                if field_type == 'numeric':
                    # Check for impossible values
                    if any(keyword in col_lower for keyword in ['عمر', 'age']):
                        validity_issues += len(col_data[(col_data < 0) | (col_data > 150)])
                    elif any(keyword in col_lower for keyword in ['نسبة', 'percentage', '%']):
                        validity_issues += len(col_data[(col_data < 0) | (col_data > 100)])
                    elif any(keyword in col_lower for keyword in ['قيمة', 'amount', 'price']):
                        validity_issues += len(col_data[col_data < 0])

                elif field_type == 'text':
                    # Check for invalid text patterns
                    for val in col_data:
                        val_str = str(val).strip()
                        # Email fields should contain @
                        if any(keyword in col_lower for keyword in ['email', 'بريد']) and '@' not in val_str:
                            validity_issues += 1
                        # Phone fields should be mostly numeric
                        elif any(keyword in col_lower for keyword in ['phone', 'هاتف']) and not re.match(r'^[\d\s\-\+\(\)\.]+$', val_str):
                            validity_issues += 1

                # 5. Statistical anomaly detection
                statistical_anomalies = 0
                if field_type == 'numeric' and len(col_data) > 30:
                    # Use multiple statistical methods
                    Q1 = col_data.quantile(0.25)
                    Q3 = col_data.quantile(0.75)
                    IQR = Q3 - Q1

                    # Count extreme outliers (beyond 3*IQR)
                    extreme_outliers = col_data[(col_data < Q1 - 3*IQR) | (col_data > Q3 + 3*IQR)]
                    statistical_anomalies = len(extreme_outliers)

                # Remove duplicates from outliers
                outliers = list(set(outliers))

                # === COMPREHENSIVE QUALITY SCORING ===
                quality_score = 100

                # Deduct points for various issues
                if completeness_score < 95:
                    quality_score -= (95 - completeness_score) * 0.5

                if len(outliers) > 0:
                    outlier_impact = min(30, (len(outliers) / len(col_data)) * 100)
                    quality_score -= outlier_impact

                if pattern_inconsistencies > 0:
                    quality_score -= min(20, (pattern_inconsistencies / len(col_data)) * 100)

                if validity_issues > 0:
                    quality_score -= min(25, (validity_issues / len(col_data)) * 100)

                quality_score = max(0, quality_score)

                # Store comprehensive outlier and quality information
                outlier_summary[col] = {
                    'outlier_count': len(outliers),
                    'outlier_percentage': round(min(100.0, max(0.0, (len(outliers) / len(col_data) * 100))), 2) if len(col_data) > 0 else 0,
                    'total_values': len(col_data),
                    'total_records': len(total_data),
                    'outlier_examples': [str(val) for val in outliers[:5]],
                    'field_type': field_type,
                    'unique_values': len(col_data.unique()),
                    'null_count': len(total_data) - len(col_data),
                    'completeness_score': round(completeness_score, 2),
                    'uniqueness_score': round(uniqueness_score, 2),
                    'quality_score': round(quality_score, 2),
                    'pattern_inconsistencies': pattern_inconsistencies,
                    'validity_issues': validity_issues,
                    'statistical_anomalies': statistical_anomalies,
                    'data_quality_issues': {
                        'duplicates_count': len(quality_issues['duplicates']),
                        'format_inconsistencies': len(quality_issues['inconsistent_formats']),
                        'suspicious_patterns': len(quality_issues['suspicious_patterns']),
                        'data_entry_errors': len(quality_issues['data_entry_errors']),
                        'missing_patterns': len(quality_issues['missing_patterns'])
                    }
                }

                # === تطبيق التعلم الذكي ===
                if AI_LEARNING_AVAILABLE:
                    try:
                        ai_engine = get_safe_ai_learning_engine()
                        if ai_engine:
                            learned_results = ai_engine.apply_learned_detection(col_data, col)

                            if learned_results['confidence'] > 0.5:
                                # دمج النتائج المتعلمة مع النتائج التقليدية
                                learned_outliers = learned_results.get('outliers', [])
                                learned_quality_issues = learned_results.get('quality_issues', [])

                                # إضافة القيم الشاذة المكتشفة بالتعلم الذكي
                                outliers.extend(learned_outliers)
                                outliers = list(set(outliers))  # إزالة التكرارات

                                # تحديث معلومات الجودة
                                outlier_summary[col]['ai_enhanced'] = True
                                outlier_summary[col]['ai_confidence'] = learned_results['confidence']
                                outlier_summary[col]['ai_applied_rules'] = learned_results.get('applied_rules', [])

                                logger.info(f"[AI_LEARNING] تم تطبيق التعلم الذكي على '{col}': ثقة {learned_results['confidence']:.2f}, قواعد مطبقة: {len(learned_results['applied_rules'])}")

                    except Exception as e:
                        logger.error(f"[AI_LEARNING] خطأ في تطبيق التعلم الذكي للحقل '{col}': {e}")

                # Enhanced logging
                if len(outliers) > 0 or quality_score < 80:
                    logger.info(f"[DATA_QUALITY] Field '{col}': {len(outliers)} outliers ({outlier_summary[col]['outlier_percentage']:.1f}%), Quality Score: {quality_score:.1f}")
                    if len(outliers) > 0:
                        logger.info(f"[DATA_QUALITY] Examples: {outlier_summary[col]['outlier_examples']}")
                    if quality_issues['duplicates']:
                        logger.info(f"[DATA_QUALITY] Excessive duplicates detected: {len(quality_issues['duplicates'])} patterns")
                    if quality_issues['suspicious_patterns']:
                        logger.info(f"[DATA_QUALITY] Suspicious patterns detected: {len(quality_issues['suspicious_patterns'])} cases")

            except Exception as e:
                logger.error(f"[DATA_QUALITY] Error analyzing field '{col}': {e}")
                outlier_summary[col] = {
                    'outlier_count': 0,
                    'outlier_percentage': 0,
                    'total_values': len(df[col].dropna()) if col in df.columns else 0,
                    'total_records': len(df[col]) if col in df.columns else 0,
                    'outlier_examples': [],
                    'field_type': 'unknown',
                    'quality_score': 0,
                    'error': str(e)
                }
    
    
    
       
       
       
       
       
       
     

        # قائمة الحقول الثابتة التي يجب استبعادها من التحليل
        static_fields_to_exclude = {
            'PortCode', 'PortName', 'DeclarationNumber', 'DeclarationType',
            'CountryCode', 'CurrencyCode', 'HSCode', 'ItemNumber',
            'SerialNumber', 'BatchNumber', 'ReferenceNumber'
        }

        # Overall outlier statistics
        total_outliers = sum(info['outlier_count'] for info in outlier_summary.values())
        fields_with_outliers = sum(1 for info in outlier_summary.values() if info['outlier_count'] > 0)
        total_values_analyzed = sum(info['total_values'] for info in outlier_summary.values())

        logger.info(f"[OUTLIER_SUMMARY] Total outliers detected: {total_outliers}")
        logger.info(f"[OUTLIER_SUMMARY] Fields with outliers: {fields_with_outliers}")
        logger.info(f"[OUTLIER_SUMMARY] Total values analyzed: {total_values_analyzed}")
        logger.info(f"[ANALYSIS] Static fields to exclude: {static_fields_to_exclude}")

        # تخصيص إعدادات التحليل لكل عمود بناءً على نوع البيانات والاستخدام المتوقع
        column_settings = {
            # أعمدة الأسماء والمعلومات الشخصية
            'الاسم': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-4',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات التجارية',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,50}$',
                'cross_field_rules': 'مرتبط باسم الأم والجنسية',
                'show_in_table': False  # إخفاء هذا الحقل من الجدول
            },
            'اسم الأم': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-4',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات التجارية',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,50}$',
                'cross_field_rules': 'مرتبط بالاسم والجنسية',
                'show_in_table': True  # إظهار هذا الحقل في الجدول
            },

            # أعمدة التصنيفات المحدودة
            'الجنس': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1',
                'limited_values': ['ذكر', 'أنثى', 'ذكر', 'انثى', 'male', 'female', 'Male', 'Female'],
                'data_source': 'يدوي',
                'data_steward': 'مدير الجودة',
                'regex_pattern': r'^(ذكر|أنثى|male|female)$',
                'cross_field_rules': 'لا توجد قواعد متقاطعة محددة',
                'show_in_table': True
            },

            # أعمدة التواريخ
            'التاريخ': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': 'لا ينطبق',
                'limited_values': [],
                'data_source': 'نظامي',
                'data_steward': 'مدير النظام',
                'regex_pattern': r'^\d{4}-\d{2}-\d{2}$',
                'cross_field_rules': 'يجب أن يكون في الماضي أو الحاضر',
                'show_in_table': False  # إخفاء هذا الحقل من الجدول
            },
            'تاريخ الميلاد': {
                'importance': 'عالية',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': 'لا ينطبق',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات التجارية',
                'regex_pattern': r'^\d{4}-\d{2}-\d{2}$',
                'cross_field_rules': 'يجب أن يكون قبل التاريخ الحالي',
                'show_in_table': True  # إظهار هذا الحقل في الجدول
            },

            # أعمدة العناوين والمعلومات الجغرافية
            'العنوان': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '3-10',
                'limited_values': [],
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات التجارية',
                'regex_pattern': r'^[\u0600-\u06FF\s\d\-\,]{10,200}$',
                'cross_field_rules': 'مرتبط بالمحافظة والمديرية',
                'show_in_table': False  # إخفاء هذا الحقل من الجدول
            },
            'المحافظة': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': ['صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب', 'ذمار', 'الضالع', 'لحج', 'أبين', 'شبوة', 'مأرب', 'الجوف', 'صعدة', 'حجة', 'عمران', 'البيضاء', 'ريمة', 'المحويت', 'حضرموت', 'وادي حضرموت', 'سقطرى'],
                'data_source': 'تكامل خارجي',
                'data_steward': 'مدير التكامل',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,20}$',
                'cross_field_rules': 'مرتبطة بالمديرية والبلد',
                'show_in_table': True
            },
            'المديرية': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-2',
                'limited_values': [],  # كثيرة جداً لتحديدها
                'data_source': 'تكامل خارجي',
                'data_steward': 'مدير التكامل',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,30}$',
                'cross_field_rules': 'مرتبطة بالمحافظة والعنوان',
                'show_in_table': True
            },

            # أعمدة التصنيفات
            'المهنة': {
                'importance': 'منخفضة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1-3',
                'limited_values': [],  # متنوعة جداً
                'data_source': 'يدوي',
                'data_steward': 'مدير البيانات التجارية',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,50}$',
                'cross_field_rules': 'لا توجد قواعد متقاطعة محددة',
                'show_in_table': False  # إخفاء هذا الحقل من الجدول
            },
            'الجنسية': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1',
                'limited_values': ['يمني', 'يمنية', 'سعودي', 'سعودية', 'إماراتي', 'إماراتية', 'كويتي', 'كويتية', 'قطري', 'قطرية', 'عماني', 'عمانية', 'بحريني', 'بحرينية', 'أردني', 'أردنية', 'مصري', 'مصرية', 'سوري', 'سورية', 'لبناني', 'لبنانية', 'فلسطيني', 'فلسطينية', 'عراقي', 'عراقية', 'ليبي', 'ليبية', 'تونسي', 'تونسية', 'جزائري', 'جزائرية', 'مغربي', 'مغربية', 'سوداني', 'سودانية'],
                'data_source': 'تكامل خارجي',
                'data_steward': 'مدير التكامل',
                'regex_pattern': r'^[\u0600-\u06FF]{3,15}$',
                'cross_field_rules': 'مرتبطة بالبلد ورقم الجواز',
                'show_in_table': True
            },
            'البلد': {
                'importance': 'متوسطة',
                'repetition_allowed': 'قابلة للتكرار',
                'sequence_required': 'لا',
                'expected_words': '1',
                'limited_values': ['اليمن', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'عمان', 'البحرين', 'الأردن', 'مصر', 'سوريا', 'لبنان', 'فلسطين', 'العراق', 'ليبيا', 'تونس', 'الجزائر', 'المغرب', 'السودان'],
                'data_source': 'تكامل خارجي',
                'data_steward': 'مدير التكامل',
                'regex_pattern': r'^[\u0600-\u06FF\s]{2,20}$',
                'cross_field_rules': 'مرتبطة بالجنسية والمحافظة',
                'show_in_table': True
            }
        }

        analysis_results = []

        for col in all_columns_to_analyze:
            # استبعاد الحقول الثابتة من التحليل
            if col in static_fields_to_exclude:
                logger.info(f"[ANALYSIS] Excluding static field '{col}' from analysis as requested")
                continue

            col_data = df[col].dropna()
            total_records = len(col_data)

            if total_records == 0:
                logger.info(f"[ANALYSIS] Skipping column '{col}' - no data")
                continue

            logger.info(f"[ANALYSIS] Analyzing column '{col}' with {total_records} records")

            # البحث عن إعدادات العمود بناءً على الاسم أو الكلمات المفتاحية
            settings = {}
            col_lower = str(col).lower()

            # البحث في إعدادات الأعمدة المخصصة
            for key, setting in column_settings.items():
                if key.lower() in col_lower or any(keyword in col_lower for keyword in key.lower().split()):
                    settings = setting.copy()
                    break

            # البحث عن إعدادات العمود بناءً على الاسم أو الكلمات المفتاحية
            settings = {}
            col_lower = str(col).lower()

            # أولوية 1: التحقق من إعدادات المستخدم المخصصة أولاً
            if col in field_settings:
                settings = field_settings[col].copy()
                logger.info(f"Using user custom settings for field '{col}'")
            else:
                # أولوية 2: البحث في إعدادات الأعمدة المخصصة مع مطابقة أفضل
                for key, setting in column_settings.items():
                    key_lower = key.lower()
                    # مطابقة دقيقة أولاً
                    if key_lower == col_lower:
                        settings = setting.copy()
                        logger.info(f"Exact match found for field '{col}' with key '{key}'")
                        break
                    # مطابقة جزئية للكلمات الرئيسية
                    elif any(keyword in col_lower for keyword in key_lower.split()):
                        settings = setting.copy()
                        logger.info(f"Partial match found for field '{col}' with key '{key}'")
                        break

                # إذا لم يتم العثور على إعدادات، استخدم الإعدادات الافتراضية العامة
                if not settings:
                    settings = {}
                    logger.info(f"Using general default settings for field '{col}'")

            # التأكد من أن الإعدادات تحتوي على show_in_table
            if 'show_in_table' not in settings:
                settings['show_in_table'] = True

            # استخراج الإعدادات مع القيم الافتراضية
            importance = settings.get('importance', 'متوسطة')
            repetition_allowed = settings.get('repetition_allowed', 'قابلة للتكرار')
            sequence_required = settings.get('sequence_required', 'لا')
            expected_words = settings.get('expected_words', 'لا ينطبق')
            limited_values = settings.get('limited_values', [])
            data_source = settings.get('data_source', 'يدوي')
            data_steward = settings.get('data_steward', 'مدير الجودة')
            regex_pattern = settings.get('regex_pattern', 'غير محدد')
            cross_field_rules = settings.get('cross_field_rules', 'لا توجد قواعد متقاطعة محددة')
            show_in_table = settings.get('show_in_table', True)  # إظهار في الجدول افتراضياً

            # تخطي الحقول التي لا يجب إظهارها في الجدول
            if not show_in_table:
                logger.info(f"[ANALYSIS] Skipping column '{col}' - hidden from table display per settings")
                continue

            # التأكد من أن الحقل له إعدادات في column_settings
            if col not in column_settings:
                column_settings[col] = {
                    'importance': importance,
                    'repetition_allowed': repetition_allowed,
                    'sequence_required': sequence_required,
                    'expected_words': expected_words,
                    'limited_values': limited_values,
                    'data_source': data_source,
                    'data_steward': data_steward,
                    'regex_pattern': regex_pattern,
                    'cross_field_rules': cross_field_rules,
                    'show_in_table': show_in_table
                }
                logger.info(f"[ANALYSIS] Ensured settings exist for field '{col}'")

            # إضافة إعدادات مفقودة للحقول الموجودة في البيانات
            if col not in column_settings:
                # إضافة إعدادات افتراضية للحقول الجديدة
                column_settings[col] = {
                    'importance': 'متوسطة',
                    'repetition_allowed': 'قابلة للتكرار',
                    'sequence_required': 'لا',
                    'expected_words': 'لا ينطبق',
                    'limited_values': [],
                    'data_source': 'يدوي',
                    'data_steward': 'مدير الجودة',
                    'regex_pattern': 'غير محدد',
                    'cross_field_rules': 'لا توجد قواعد متقاطعة محددة',
                    'show_in_table': True
                }
                logger.info(f"[ANALYSIS] Added default settings for new field '{col}'")

            # تحديث إعدادات الحقل في column_settings إذا كانت مختلفة
            if col in column_settings:
                current_settings = column_settings[col]
                # تحديث الإعدادات إذا تغيرت
                if (current_settings.get('importance') != importance or
                    current_settings.get('repetition_allowed') != repetition_allowed or
                    current_settings.get('data_source') != data_source):
                    column_settings[col].update({
                        'importance': importance,
                        'repetition_allowed': repetition_allowed,
                        'sequence_required': sequence_required,
                        'expected_words': expected_words,
                        'limited_values': limited_values,
                        'data_source': data_source,
                        'data_steward': data_steward,
                        'regex_pattern': regex_pattern,
                        'cross_field_rules': cross_field_rules,
                        'show_in_table': show_in_table
                    })
                    logger.info(f"[ANALYSIS] Updated settings for field '{col}'")

            # Basic analysis
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            zero_count = (df[col] == 0).sum()
            dash_count = (df[col] == '---').sum()
            star_count = (df[col] == '***').sum()

            # Calculate missing percentage correctly (relative to total rows, not just non-null rows) with safety checks
            total_rows = max(1, len(df))
            missing_count = max(0, null_count + empty_count)
            missing_percentage = min(100.0, max(0.0, (missing_count / total_rows * 100))) if total_rows > 0 else 0

            # Length analysis
            lengths = col_data.astype(str).str.len()
            fixed_length = lengths.nunique() == 1
            avg_length = lengths.mean() if not lengths.empty else 0

            # Word count analysis
            word_counts = col_data.astype(str).str.split().str.len()
            avg_words = word_counts.mean() if not word_counts.empty else 0

            # Duplicate analysis
            duplicates = col_data.duplicated().sum()

            # Unique values analysis
            unique_values = col_data.unique()
            unique_count = int(len(unique_values))

            # Detailed duplicate analysis
            duplicate_details = {}
            if duplicates > 0:
                value_counts = col_data.value_counts()
                duplicate_values = value_counts[value_counts > 1]
                duplicate_details = duplicate_values.to_dict()

            # Check for limited values violations
            outlier_count = 0
            if limited_values and len(limited_values) > 0:
                # Count values that are not in the limited values list
                outlier_count = sum(1 for val in col_data if str(val) not in limited_values)
                # Ensure outlier_count doesn't exceed total records
                outlier_count = min(outlier_count, len(col_data))
                logger.info(f"[ANALYSIS] Field '{col}' has {outlier_count} values not in limited list: {limited_values}")
            else:
                # If no limited values are specified, use statistical outlier detection for numeric fields
                if pd.api.types.is_numeric_dtype(col_data):
                    try:
                        Q1 = col_data.quantile(0.25)
                        Q3 = col_data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                        outlier_count = len(outliers)
                    except Exception as e:
                        logger.warning(f"Error calculating statistical outliers for {col}: {e}")
                        outlier_count = 0

            # Check word count violations
            word_violations = 0
            logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': expected_words='{expected_words}', word_counts={word_counts}")
            if expected_words and expected_words != 'لا ينطبق' and isinstance(expected_words, str):
                try:
                    if '-' in expected_words:
                        # Handle range like "1-4"
                        parts = expected_words.split('-')
                        if len(parts) == 2:
                            min_words = int(parts[0].strip())
                            max_words = int(parts[1].strip())
                            word_violations = sum(1 for wc in word_counts if wc < min_words or wc > max_words)
                            logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': range validation {min_words}-{max_words}, violations={word_violations}")
                            # Log detailed violations
                            violations_detail = [f"{i}:{wc}" for i, wc in enumerate(word_counts) if wc < min_words or wc > max_words]
                            if violations_detail:
                                logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': violations at positions: {violations_detail[:5]}")
                        else:
                            expected_count = int(parts[0])
                            word_violations = sum(1 for wc in word_counts if wc != expected_count)
                            logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': exact count validation {expected_count}, violations={word_violations}")
                    else:
                        expected_count = int(expected_words)
                        word_violations = sum(1 for wc in word_counts if wc != expected_count)
                        logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': exact count validation {expected_count}, violations={word_violations}")
                        # Log detailed violations
                        violations_detail = [f"{i}:{wc}" for i, wc in enumerate(word_counts) if wc != expected_count]
                        if violations_detail:
                            logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': violations at positions: {violations_detail[:5]}")
                except (ValueError, IndexError, TypeError) as e:
                    word_violations = 0
                    logger.warning(f"[WORD_GAPS_DEBUG] Field '{col}': error parsing expected_words '{expected_words}': {e}")
            else:
                logger.info(f"[WORD_GAPS_DEBUG] Field '{col}': no validation (expected_words='{expected_words}')")

            # Enhanced field type detection using sector manager
            field_type = 'نصي'  # Default
            data_source = 'يدوي'  # Default
            data_steward = 'مدير البيانات التجارية'  # Default
            regex_pattern = 'غير محدد'  # Default
            repetition_desc = repetition_allowed
            cross_field_rules = 'لا توجد قواعد متقاطعة محددة'
    
            # Store DataFrame reference for duplicate analysis
            global_df_reference = df if 'df' in locals() else None

            # Use sector manager for intelligent field detection
            if sector_manager and hasattr(sector_manager, 'detect_column_by_alias'):
                try:
                    detected_type = 'text'  # Default fallback
                except AttributeError:
                    detected_type = None
                if detected_type:
                    # Map detected types to display types
                    type_mapping = {
                        'person_name': 'نصي (أسماء)',
                        'mother_name': 'نصي (أسماء)',
                        'gender': 'نصي',
                        'birth_date': 'تاريخ',
                        'main_numbers': 'رمز/رقم',
                        'manual_numbers': 'رمز/رقم',
                        'document_type': 'نصي',
                        'document_number': 'رمز/رقم',
                        'document_date': 'تاريخ',
                        'document_place': 'نصي (أسماء)',
                        'country': 'رمز/رقم',
                        'province': 'نصي (أسماء)',
                        'district': 'نصي (أسماء)',
                        'city': 'نصي (أسماء)',
                        'nationality': 'نصي',
                        'insurance_status': 'نصي',
                        'insurance_sector': 'نصي',
                        'subscription_status': 'نصي',
                        'branch': 'نصي',
                        'phone_number': 'رمز/رقم',
                        'email': 'نصي',
                        'address': 'نصي',
                        'occupation': 'نصي',
                        'salary': 'رقمي',
                        'company_name': 'نصي (أسماء)',
                        'declaration_type': 'نصي',
                        'tax_number': 'رمز/رقم',
                        'financial_responsible': 'نصي (أسماء)',
                        'declarant_reference': 'رمز/رقم',
                        'clearing_agent_tax': 'رمز/رقم',
                        'clearing_agent_name': 'نصي (أسماء)',
                        'item_number': 'رمز/رقم',
                        'commodity_name': 'نصي (أسماء)',
                        'hs_code': 'رمز/رقم',
                        'brand': 'نصي'
                    }
                    field_type = type_mapping.get(detected_type, 'نصي')

                    # Set data source based on field type
                    if detected_type in ['birth_date', 'document_date', 'registration_date', 'receipt_date', 'filtration_date']:
                        data_source = 'نظامي'
                        data_steward = 'مدير النظام'
                    elif detected_type in ['country', 'currency_code', 'origin_country_code']:
                        data_source = 'تكامل خارجي'
                        data_steward = 'مدير التكامل'
                    elif detected_type in ['person_name', 'mother_name', 'company_name', 'commodity_name', 'clearing_agent_name', 'financial_responsible']:
                        data_source = 'يدوي'
                        data_steward = 'مدير البيانات التجارية'
                    else:
                        data_source = 'يدوي'
                        data_steward = 'مدير الجودة'

                    # Set regex patterns for specific field types
                    if detected_type == 'country' or 'بلد' in col.lower():
                        regex_pattern = '^[A-Z]{2}$'
                    elif detected_type == 'currency_code' or 'عملة' in col.lower():
                        regex_pattern = '^[A-Z]{3}$'
                    elif 'تاريخ' in col.lower() or detected_type in ['birth_date', 'document_date']:
                        regex_pattern = '^\\d{4}-\\d{2}-\\d{2}$'
                    elif detected_type in ['main_numbers', 'manual_numbers', 'document_number'] and fixed_length != 'متغير':
                        regex_pattern = f'^\\d{{{int(avg_length)}}}$'

                    # Set repetition rules
                    if detected_type in ['main_numbers', 'document_number', 'policy_number', 'receipt_number', 'declaration_number']:
                        repetition_desc = 'فريدة (مفتاح تعريفي)'
                    elif detected_type in ['person_name', 'company_name', 'commodity_name']:
                        repetition_desc = 'قابلة للتكرار'
                    else:
                        repetition_desc = 'قابل للتكرار'

                    # Set cross-field rules
                    if 'بيان' in col.lower():
                        cross_field_rules = 'يجب أن يكون فريداً، مرتبط بتاريخ البيان'
                    elif 'مستورد' in col.lower():
                        cross_field_rules = 'مرتبط برقم البيان والمنفذ'
                    elif 'بلد' in col.lower():
                        cross_field_rules = 'يجب أن يطابق معايير ISO'
                    elif 'تاريخ' in col.lower():
                        cross_field_rules = 'يجب أن يكون في الماضي أو الحاضر'
                    else:
                        cross_field_rules = 'لا توجد قواعد متقاطعة محددة'

            # Convert any Timestamp objects to strings for JSON serialization
            duplicate_details_clean = {}
            if duplicate_details:
                for k, v in duplicate_details.items():
                    # Convert keys and values to strings if they are Timestamp objects
                    key_str = str(k) if hasattr(k, 'isoformat') else str(k)
                    val_str = str(v) if hasattr(v, 'isoformat') else str(v)
                    duplicate_details_clean[key_str] = val_str

            # Create sample unique values for display
            unique_samples = []
            if col_data is not None and len(col_data) > 0:
                unique_vals = col_data.dropna().unique()
                # Get up to 5 sample unique values, but filter out problematic values
                sample_size = min(5, len(unique_vals))
                unique_samples = []
                for val in unique_vals[:sample_size]:
                    str_val = str(val)
                    # Skip very long values that might break display
                    if len(str_val) > 100:
                        str_val = str_val[:97] + "..."
                    # Escape HTML characters to prevent XSS
                    str_val = str_val.replace('<', '<').replace('>', '>').replace('"', '"').replace("'", "'")
                    unique_samples.append(str_val)
                unique_samples_str = ', '.join(unique_samples[:3]) + ('...' if len(unique_samples) > 3 else '')
            else:
                unique_samples_str = 'لا توجد قيم فريدة'

            # Create outlier examples using comprehensive detection
            outlier_examples = []
            try:
                # Use the comprehensive outlier detection function
                detected_outliers = detect_outliers_comprehensive(col_data, col, {})
                if detected_outliers and len(detected_outliers) > 0:
                    # Get first 3 actual outlier examples
                    outlier_examples = [str(val) for val in detected_outliers[:3]]
                elif outlier_count > 0 and limited_values and len(limited_values) > 0:
                    # Fallback: Get actual outlier values based on limited values
                    outlier_vals = [str(val) for val in col_data if str(val) not in limited_values][:3]
                    outlier_examples = outlier_vals
                elif outlier_count > 0:
                    # Fallback: For numeric outliers, show some examples
                    if pd.api.types.is_numeric_dtype(col_data):
                        outlier_vals = []
                        if len(col_data) > 0:
                            # Get some values that might be outliers (simple approach)
                            sorted_data = col_data.sort_values()
                            outlier_vals = [str(sorted_data.iloc[0]), str(sorted_data.iloc[-1])]  # min and max
                        outlier_examples = outlier_vals[:3]
                    else:
                        # For text data, show some examples of values that appear infrequently
                        value_counts = col_data.value_counts()
                        rare_values = value_counts[value_counts == 1].head(3).index.tolist()
                        outlier_examples = [str(val) for val in rare_values]
            except Exception as e:
                logger.warning(f"Error detecting outliers for column {col}: {e}")
                outlier_examples = []

            outlier_examples_str = ', '.join(outlier_examples) if outlier_examples else 'لا توجد أمثلة محددة'

            # Calculate duplicate percentage with enhanced safety checks
            duplicate_percentage = min(100.0, max(0.0, (duplicates / total_records * 100))) if total_records > 0 else 0

            # Create unique details for export (list of unique values)
            unique_details = {}
            if len(unique_values) > 0:
                for i, val in enumerate(unique_values):
                    unique_details[str(i)] = str(val)

            analysis_results.append({
                'field_name': str(col),  # Ensure field name is string
                'importance': str(importance),  # Ensure importance is string
                'repetition_allowed': str(repetition_desc),  # Ensure repetition_allowed is string
                'null_values': int(null_count + empty_count),
                'missing_percentage': round(float(min(100.0, missing_percentage)), 2),
                'fixed_length': str(int(avg_length)) if fixed_length else 'متغير',  # Ensure fixed_length is string
                'sequence_required': str(sequence_required),  # Ensure sequence_required is string
                'expected_words': str(expected_words),  # Ensure expected_words is string
                'limited_values': [str(x) for x in limited_values] if limited_values else [],  # Ensure limited_values are strings
                'quality_notes': f'جودة متوسطة - {int(duplicates)} قيمة متكررة من أصل {int(unique_count)} قيمة فريدة',
                'total_records': int(total_records),
                'unique_values': int(unique_count),
                'unique_values_display': f"{unique_count} قيمة فريدة ({', '.join([str(val) for val in unique_values[:3]])}{'...' if len(unique_values) > 3 else ''})",
                'unique_values_formatted': format_unique_details_for_table(str(col), int(unique_count), unique_details),  # Formatted display for table with count and export dropdown
                'duplicates': format_duplicate_details_for_table(str(col), int(duplicates), duplicate_details_clean, df),  # Formatted display for table with count and details button
                'duplicates_count': int(duplicates),  # Raw count for calculations and export functions
                # Calculate percentages with capping at 100.0 and rounding to 2 decimal places
                'duplicate_percentage': round(float(min(100.0, max(0.0, duplicate_percentage))), 2),
                'duplicate_percentage_display': f"{round(float(duplicate_percentage), 2):.2f}",
                'unique_percentage': min(100.0, round(float((unique_count / total_records) * 100 if total_records > 0 else 0), 2)),
                'unique_percentage_display': f"{min(100.0, round(float((unique_count / total_records) * 100 if total_records > 0 else 0), 2)):.2f}",
                'duplicate_details': duplicate_details_clean,  # Use cleaned duplicate_details
                'unique_details': unique_details,  # Unique values details for export
                'unique_samples': unique_samples_str,
                'word_violations': int(word_violations),
                'outlier_values': int(outlier_count),
                'outlier_percentage': min(100.0, max(0.0, round(float((outlier_count / total_records) * 100 if total_records > 0 else 0), 2))),
                'outlier_examples': outlier_examples,  # Add actual outlier examples
                'data_source': str(data_source),  # Ensure data_source is string
                'data_steward': str(data_steward),  # Ensure data_steward is string
                'last_checked': datetime.now().strftime('%d/%m/%Y'),  # Already a string
                'cross_field_rules': str(cross_field_rules),  # Ensure cross_field_rules is string
                'regex_pattern': str(regex_pattern),  # Ensure regex_pattern is string
                'field_type': str(field_type),  # Ensure field_type is string
            })

        # Quality metrics
        quality_metrics = {
            'irregular_repetition': sum(1 for r in analysis_results if r.get('duplicates_count', 0) > 0),
            'input_gaps': sum(1 for r in analysis_results if r.get('null_values', 0) > 0),
            'word_gaps': sum(1 for r in analysis_results if r.get('word_violations', 0) > 0),
            'length_irregularities': sum(1 for r in analysis_results if r.get('fixed_length') == 'متغير'),
            'sequence_irregularities': sum(1 for r in analysis_results if r.get('sequence_required') == 'نعم'),  # Placeholder logic
            'outlier_values': sum(1 for r in analysis_results if r.get('outlier_values', 0) > 0),
            'total': len(analysis_results),
            'average': 0.0  # Will be calculated
        }

        total_issues = sum(quality_metrics.values()) - quality_metrics['total']  # Exclude total and average
        quality_metrics['average'] = float(total_issues / len(analysis_results)) if analysis_results else 0.0

        # Create quantitative inspection results table
        quantitative_inspection = {
            'التكرار غير المنطقي (خارج إطار شروط التكرار)': {
                'عدد السجلات': quality_metrics.get('irregular_repetition', 0),
                'نسبة': f"{quality_metrics.get('irregular_repetition', 0) / quality_metrics.get('total', 1) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%",
                'قوائم مرفقة': 'قائمة بالقيم المكررة غير المسموح بها',
                'نوع المخطط': 'عمود مكدس'
            },
            'قائمة نواقص الإدخال': {
                'عدد السجلات': quality_metrics.get('input_gaps', 0),
                'نسبة': f"{quality_metrics.get('input_gaps', 0) / quality_metrics.get('total', 1) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%",
                'قوائم مرفقة': 'قائمة بالسجلات ذات القيم الفارغة أو الصفرية',
                'نوع المخطط': 'عمود مكدس'
            },
            'قائمة الاختلالات في الخانات': {
                'عدد السجلات': quality_metrics.get('length_irregularities', 0),
                'نسبة': f"{quality_metrics.get('length_irregularities', 0) / quality_metrics.get('total', 1) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%",
                'قوائم مرفقة': 'القيم ذات الطول غير الصحيح',
                'نوع المخطط': 'عمود مكدس'
            },
            'قائمة اختلالات التسلسل (إن كان ضرورياً)': {
                'عدد السجلات': quality_metrics.get('sequence_irregularities', 0),
                'نسبة': f"{quality_metrics.get('sequence_irregularities', 0) / quality_metrics.get('total', 1) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%",
                'قوائم مرفقة': 'القيم غير المتسلسلة حسب المعيار',
                'نوع المخطط': 'عمود مكدس'
            },
            'قائمة نواقص الكلمات (إن وجد، مثل ترقيم مزدوج أو رموز غير رقمية)': {
                'عدد السجلات': quality_metrics.get('word_gaps', 0),
                'نسبة': f"{quality_metrics.get('word_gaps', 0) / quality_metrics.get('total', 1) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%",
                'قوائم مرفقة': 'القيم غير رقمية أو مختلطة',
                'نوع المخطط': 'عمود مكدس'
            },
            'الإجمالي': {
                'عدد السجلات': sum([quality_metrics.get(k, 0) for k in ['irregular_repetition', 'input_gaps', 'length_irregularities', 'sequence_irregularities', 'word_gaps']]),
                'نسبة': '—',
                'قوائم مرفقة': '—',
                'نوع المخطط': '—'
            },
            'المتوسط': {
                'عدد السجلات': '—',
                'نسبة': '—',
                'قوائم مرفقة': '—',
                'نوع المخطط': '—'
            }
        }

        logger.info(f"[ANALYSIS] Returning {len(analysis_results)} analyzed fields")
        for i, field in enumerate(analysis_results[:3]):  # Show first 3 fields
            logger.info(f"[ANALYSIS] Field {i+1}: {field['field_name']} - {field.get('total_records', 0)} records")
        if analysis_results:
            logger.info(f"[ANALYSIS] Sample field data keys: {list(analysis_results[0].keys())}")

        # Perform numeric analysis if there are numeric columns
        numeric_analysis = {}
        if numeric_columns:
            logger.info(f"Performing numeric analysis on {len(numeric_columns)} columns")
            for col in numeric_columns[:10]:  # Limit to first 10 for performance
                try:
                    col_data = df[col].dropna()
                    if len(col_data) > 0:
                        # Calculate basic statistics
                        mean_val = float(col_data.mean()) if not col_data.empty else 0.0
                        std_val = float(col_data.std()) if len(col_data) > 1 else 0.0
                        min_val = float(col_data.min()) if not col_data.empty else 0.0
                        max_val = float(col_data.max()) if not col_data.empty else 0.0

                        # Detect outliers using IQR method
                        Q1 = col_data.quantile(0.25)
                        Q3 = col_data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        outliers_count = int(((col_data < lower_bound) | (col_data > upper_bound)).sum())

                        numeric_analysis[col] = {
                            'count': len(col_data),
                            'mean': mean_val,
                            'std': std_val,
                            'min': min_val,
                            'max': max_val,
                            'zeros': int((col_data == 0).sum()),
                            'negatives': int((col_data < 0).sum()),
                            'outliers': outliers_count,
                            'range': max_val - min_val if not col_data.empty else 0.0
                        }
                except Exception as e:
                    logger.debug(f"Error in numeric analysis for column {col}: {e}")

        # Add memory monitoring results
        final_memory = None
        memory_analysis = None
        if memory_monitor:
            final_memory = memory_monitor.take_snapshot()
            memory_analysis = memory_monitor.get_memory_analysis()

            memory_usage = {
                'initial_mb': initial_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024),
                'final_mb': final_memory.get('process', {}).get('memory_rss', 0) / (1024 * 1024),
                'peak_mb': memory_analysis.get('tracemalloc_stats', {}).get('peak_mb', 0) if memory_analysis else 0,
                'gc_collections': memory_analysis.get('gc_stats', {}).get('collections', [0, 0, 0]) if memory_analysis else [0, 0, 0]
            }

            logger.info(f"[MEMORY] Text analysis completed - Memory usage: {memory_usage['initial_mb']:.1f}MB -> {memory_usage['final_mb']:.1f}MB (peak: {memory_usage['peak_mb']:.1f}MB)")

            # === التعلم التراكمي من البيانات المحللة ===
            if AI_LEARNING_AVAILABLE:
                try:
                    ai_engine = get_safe_ai_learning_engine()
                    if ai_engine:
                        # تجميع نتائج التحليل للتعلم
                        combined_analysis_results = {}

                        # دمج نتائج التحليل النصي
                        for field_result in analysis_results:
                            field_name = field_result.get('field_name')
                            if field_name:
                                combined_analysis_results[field_name] = {
                                    'outlier_count': field_result.get('outlier_values', 0),
                                    'outlier_examples': field_result.get('outlier_examples', []),
                                    'duplicates_count': field_result.get('duplicates_count', 0),
                                    'unique_values': field_result.get('unique_values', 0),
                                    'field_type': 'text'
                                }

                        # دمج نتائج التحليل الرقمي
                        for numeric_result in numeric_analysis:
                            field_name = numeric_result.get('field_name')
                            if field_name:
                                if field_name not in combined_analysis_results:
                                    combined_analysis_results[field_name] = {}
                                combined_analysis_results[field_name].update({
                                    'outlier_count': numeric_result.get('outlier_count', 0),
                                    'outlier_examples': numeric_result.get('outlier_examples', []),
                                    'field_type': 'numeric'
                                })

                        # دمج نتائج الكشف الشامل
                        for field_name, outlier_info in outlier_summary.items():
                            if field_name not in combined_analysis_results:
                                combined_analysis_results[field_name] = {}
                            combined_analysis_results[field_name].update({
                                'outlier_count': outlier_info.get('outlier_count', 0),
                                'outlier_examples': outlier_info.get('outlier_examples', []),
                                'quality_score': outlier_info.get('quality_score', 0),
                                'data_quality_issues': outlier_info.get('data_quality_issues', {})
                            })

                        # تطبيق التعلم التراكمي
                        learning_result = ai_engine.learn_from_data(df, combined_analysis_results, column_settings)

                        if learning_result.get('success'):
                            logger.info(f"[AI_LEARNING] تم التعلم التراكمي بنجاح: {learning_result['new_patterns_found']} نمط جديد في {learning_result['processing_time']:.2f} ثانية")

                            # إضافة معلومات التعلم للنتائج
                            learning_info = {
                                'ai_learning_applied': True,
                                'new_patterns_discovered': learning_result['new_patterns_found'],
                                'total_knowledge_base_size': learning_result['total_knowledge_base_size'],
                                'learning_processing_time': learning_result['processing_time']
                            }
                        else:
                            logger.warning(f"[AI_LEARNING] فشل في التعلم التراكمي: {learning_result.get('error', 'خطأ غير معروف')}")
                            learning_info = {
                                'ai_learning_applied': False,
                                'learning_error': learning_result.get('error', 'خطأ غير معروف')
                            }
                    else:
                        learning_info = {
                            'ai_learning_applied': False,
                            'learning_error': 'فشل في الحصول على محرك التعلم الذكي'
                        }

                except Exception as e:
                    logger.error(f"[AI_LEARNING] خطأ في التعلم التراكمي: {e}")
                    learning_info = {
                        'ai_learning_applied': False,
                        'learning_error': str(e)
                    }
            else:
                learning_info = {
                    'ai_learning_applied': False,
                    'learning_error': 'محرك التعلم الذكي غير متوفر'
                }

            return jsonify({
                'field_analysis': analysis_results,
                'quality_metrics': quality_metrics,
                'quantitative_inspection': quantitative_inspection,
                'numeric_analysis': numeric_analysis,
                'total_fields': len(analysis_results),
                'memory_usage': memory_usage,
                'ai_learning_info': learning_info,
                'performance_info': {
                    'processing_method': 'streaming' if streaming_processor else 'standard',
                    'memory_monitoring': True
                }
            })
        else:
            # === التعلم التراكمي من البيانات المحللة (بدون مراقبة الذاكرة) ===
            if AI_LEARNING_AVAILABLE:
                try:
                    ai_engine = get_safe_ai_learning_engine()
                    if ai_engine:
                        # تجميع نتائج التحليل للتعلم
                        combined_analysis_results = {}

                        # دمج نتائج التحليل النصي
                        for field_result in analysis_results:
                            field_name = field_result.get('field_name')
                            if field_name:
                                combined_analysis_results[field_name] = {
                                    'outlier_count': field_result.get('outlier_values', 0),
                                    'outlier_examples': field_result.get('outlier_examples', []),
                                    'duplicates_count': field_result.get('duplicates_count', 0),
                                    'unique_values': field_result.get('unique_values', 0),
                                    'field_type': 'text'
                                }

                        # دمج نتائج التحليل الرقمي
                        for numeric_result in numeric_analysis:
                            field_name = numeric_result.get('field_name')
                            if field_name:
                                if field_name not in combined_analysis_results:
                                    combined_analysis_results[field_name] = {}
                                combined_analysis_results[field_name].update({
                                    'outlier_count': numeric_result.get('outlier_count', 0),
                                    'outlier_examples': numeric_result.get('outlier_examples', []),
                                    'field_type': 'numeric'
                                })

                        # دمج نتائج الكشف الشامل
                        for field_name, outlier_info in outlier_summary.items():
                            if field_name not in combined_analysis_results:
                                combined_analysis_results[field_name] = {}
                            combined_analysis_results[field_name].update({
                                'outlier_count': outlier_info.get('outlier_count', 0),
                                'outlier_examples': outlier_info.get('outlier_examples', []),
                                'quality_score': outlier_info.get('quality_score', 0),
                                'data_quality_issues': outlier_info.get('data_quality_issues', {})
                            })

                        # تطبيق التعلم التراكمي
                        learning_result = ai_engine.learn_from_data(df, combined_analysis_results, column_settings)

                        if learning_result.get('success'):
                            logger.info(f"[AI_LEARNING] تم التعلم التراكمي بنجاح: {learning_result['new_patterns_found']} نمط جديد")
                            learning_info = {
                                'ai_learning_applied': True,
                                'new_patterns_discovered': learning_result['new_patterns_found'],
                                'total_knowledge_base_size': learning_result['total_knowledge_base_size']
                            }
                        else:
                            learning_info = {
                                'ai_learning_applied': False,
                                'learning_error': learning_result.get('error', 'خطأ غير معروف')
                            }
                    else:
                        learning_info = {
                            'ai_learning_applied': False,
                            'learning_error': 'فشل في الحصول على محرك التعلم الذكي'
                        }

                except Exception as e:
                    logger.error(f"[AI_LEARNING] خطأ في التعلم التراكمي: {e}")
                    learning_info = {
                        'ai_learning_applied': False,
                        'learning_error': str(e)
                    }
            else:
                learning_info = {
                    'ai_learning_applied': False,
                    'learning_error': 'محرك التعلم الذكي غير متوفر'
                }

            return jsonify({
                'field_analysis': analysis_results,
                'quality_metrics': quality_metrics,
                'quantitative_inspection': quantitative_inspection,
                'numeric_analysis': numeric_analysis,
                'total_fields': len(analysis_results),
                'ai_learning_info': learning_info,
                'performance_info': {
                    'processing_method': 'streaming' if streaming_processor else 'standard',
                    'memory_monitoring': False
                }
            })

    except Exception as e:
        logger.error(f"Error in text field analysis: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === نقاط API لإدارة التعلم الذكي ===

@app.route('/api/ai-learning/status', methods=['GET'])
@login_required
def ai_learning_status():
    """الحصول على حالة محرك التعلم الذكي"""
    try:
        if not AI_LEARNING_AVAILABLE:
            return jsonify({
                'available': False,
                'error': 'محرك التعلم الذكي غير متوفر'
            })

        ai_engine = get_safe_ai_learning_engine()
        if not ai_engine:
            return jsonify({
                'available': False,
                'error': 'فشل في الحصول على محرك التعلم الذكي'
            })

        stats = ai_engine.get_learning_statistics()

        return jsonify({
            'available': True,
            'statistics': stats,
            'database_path': ai_engine.learning_db_path
        })

    except Exception as e:
        logger.error(f"[AI_LEARNING_API] خطأ في الحصول على حالة التعلم الذكي: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai-learning/export-knowledge', methods=['POST'])
@login_required
def export_ai_knowledge():
    """تصدير المعرفة المتعلمة"""
    try:
        if not AI_LEARNING_AVAILABLE:
            return jsonify({'error': 'محرك التعلم الذكي غير متوفر'}), 400

        ai_engine = get_safe_ai_learning_engine()
        if not ai_engine:
            return jsonify({'error': 'فشل في الحصول على محرك التعلم الذكي'}), 500

        knowledge_data = ai_engine.export_learned_knowledge()

        if knowledge_data:
            from datetime import datetime
            import json

            # إنشاء ملف JSON للتصدير
            export_filename = f'ai_knowledge_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

            return Response(
                json.dumps(knowledge_data, ensure_ascii=False, indent=2),
                mimetype='application/json',
                headers={'Content-Disposition': f'attachment; filename={export_filename}'}
            )
        else:
            return jsonify({'error': 'لا توجد معرفة متعلمة للتصدير'}), 400

    except Exception as e:
        logger.error(f"[AI_LEARNING_API] خطأ في تصدير المعرفة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai-learning/import-knowledge', methods=['POST'])
@login_required
def import_ai_knowledge():
    """استيراد المعرفة المتعلمة"""
    try:
        if not AI_LEARNING_AVAILABLE:
            return jsonify({'error': 'محرك التعلم الذكي غير متوفر'}), 400

        if 'file' not in request.files:
            return jsonify({'error': 'لم يتم رفع ملف'}), 400

        file = request.files['file']
        if not file.filename or not file.filename.lower().endswith('.json'):
            return jsonify({'error': 'يجب أن يكون الملف من نوع JSON'}), 400

        try:
            import json
            knowledge_data = json.load(file.stream)
        except json.JSONDecodeError:
            return jsonify({'error': 'ملف JSON غير صالح'}), 400

        ai_engine = get_safe_ai_learning_engine()
        if not ai_engine:
            return jsonify({'error': 'فشل في الحصول على محرك التعلم الذكي'}), 500

        result = ai_engine.import_learned_knowledge(knowledge_data)

        if result.get('success'):
            return jsonify({
                'success': True,
                'message': f'تم استيراد {result["imported_patterns"]} نمط بنجاح',
                'imported_patterns': result['imported_patterns']
            })
        else:
            return jsonify({'error': result.get('error', 'فشل في الاستيراد')}), 400

    except Exception as e:
        logger.error(f"[AI_LEARNING_API] خطأ في استيراد المعرفة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai-learning/reset', methods=['POST'])
@login_required
def reset_ai_learning():
    """إعادة تعيين قاعدة المعرفة"""
    try:
        if not AI_LEARNING_AVAILABLE:
            return jsonify({'error': 'محرك التعلم الذكي غير متوفر'}), 400

        # التحقق من صلاحيات المستخدم (يجب أن يكون مدير)
        if not hasattr(session, 'user_role') or session.get('user_role') != 'admin':
            return jsonify({'error': 'غير مصرح لك بهذا الإجراء'}), 403

        ai_engine = get_safe_ai_learning_engine()
        if not ai_engine:
            return jsonify({'error': 'فشل في الحصول على محرك التعلم الذكي'}), 500

        result = ai_engine.reset_knowledge_base()

        if result.get('success'):
            return jsonify({
                'success': True,
                'message': 'تم إعادة تعيين قاعدة المعرفة بنجاح'
            })
        else:
            return jsonify({'error': result.get('error', 'فشل في إعادة التعيين')}), 500

    except Exception as e:
        logger.error(f"[AI_LEARNING_API] خطأ في إعادة تعيين التعلم الذكي: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/sector/<sector_id>')
@login_required
def sector_upload(sector_id):
    """Sector upload page"""
    # Get user info from session
    user_info = {
        'full_name': session.get('username', 'مستخدم'),
        'role': session.get('role', 'user')
    }

    return render_template('sector_upload.html',
                         user=user_info,
                         sector_id=sector_id,
                         config={'system_name': 'نظام تحليل قطاع الجمارك'})

@app.route('/upload', methods=['POST'])
@login_required
def upload():
    """Handle file upload with support for large files"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            return jsonify({'error': 'No selected file'}), 400

        file = files[0]
        if not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            return jsonify({'error': 'Invalid file type'}), 400

        # Check file size
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        file_size_mb = file_size / (1024 * 1024)

        # Limit file size to prevent abuse (max 2GB)
        if file_size_mb > 2048:
            return jsonify({'error': 'File too large. Maximum size is 2GB.'}), 400

        # Save file temporarily
        import uuid
        session_id = str(uuid.uuid4())
        filename = f"{session_id}_{file.filename}"
        file_path = os.path.join('uploads', filename)

        # Ensure uploads directory exists
        os.makedirs('uploads', exist_ok=True)

        # For large files, save in chunks to avoid memory issues
        if file_size_mb > 100:
            logger.info(f"[UPLOAD] Large file ({file_size_mb:.1f}MB) - saving in chunks")
            chunk_size = 1024 * 1024  # 1MB chunks
            with open(file_path, 'wb') as f:
                while True:
                    chunk = file.read(chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
        else:
            file.save(file_path)

        # Process the file with size-appropriate method
        try:
            if file_size_mb > 50:  # Very large files - just validate and get basic info
                logger.info(f"[UPLOAD] Very large file ({file_size_mb:.1f}MB) - basic validation only")

                # Basic file validation without loading into memory
                if file.filename.lower().endswith('.csv'):
                    # For CSV, try to read just the header
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            header_line = f.readline()
                            if ',' in header_line:
                                column_count = len(header_line.split(','))
                            else:
                                column_count = 1
                        row_count = sum(1 for _ in open(file_path, 'r', encoding='utf-8')) - 1  # Approximate
                    except:
                        column_count = 0
                        row_count = 0
                else:
                    # For Excel, we need to load it but can limit rows
                    df_sample = pd.read_excel(file_path, nrows=100, engine='openpyxl')
                    column_count = len(df_sample.columns)
                    # Estimate total rows (rough approximation)
                    row_count = int((file_size_mb * 1024 * 1024) / (df_sample.memory_usage(deep=True).sum() / len(df_sample)) / 2)

                session_data = {
                    'session_id': session_id,
                    'filename': file.filename,
                    'file_path': file_path,
                    'timestamp': datetime.now().isoformat(),
                    'row_count': row_count,
                    'column_count': column_count,
                    'file_size_mb': file_size_mb,
                    'large_file': True,
                    'processing_method': 'streaming_required'
                }

            elif file_size_mb > 5:  # Large files - load sample for validation
                logger.info(f"[UPLOAD] Large file ({file_size_mb:.1f}MB) - loading sample for validation")

                if file.filename.lower().endswith('.csv'):
                    df = pd.read_csv(file_path, nrows=1000)  # Load first 1000 rows
                else:
                    df = pd.read_excel(file_path, nrows=1000, engine='openpyxl')

                session_data = {
                    'session_id': session_id,
                    'filename': file.filename,
                    'file_path': file_path,
                    'timestamp': datetime.now().isoformat(),
                    'row_count': len(df),
                    'column_count': len(df.columns),
                    'file_size_mb': file_size_mb,
                    'large_file': True,
                    'sample_loaded': True
                }

            else:  # Normal files - full processing
                logger.info(f"[UPLOAD] Normal file ({file_size_mb:.1f}MB) - full processing")

                if file.filename.lower().endswith('.csv'):
                    df = pd.read_csv(file_path)
                else:
                    df = pd.read_excel(file_path, engine='openpyxl')

                # Basic validation
                if df.empty:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    return jsonify({'error': 'File is empty'}), 400

                session_data = {
                    'session_id': session_id,
                    'filename': file.filename,
                    'file_path': file_path,
                    'timestamp': datetime.now().isoformat(),
                    'row_count': len(df),
                    'column_count': len(df.columns),
                    'file_size_mb': file_size_mb,
                    'large_file': False
                }

            logger.info(f"[UPLOAD] File uploaded successfully: {file.filename} ({file_size_mb:.1f}MB)")
            return jsonify({
                'session_id': session_id,
                'message': 'File uploaded successfully',
                'data': session_data
            })

        except Exception as e:
            logger.error(f"[UPLOAD] Error processing file: {str(e)}")
            if os.path.exists(file_path):
                os.remove(file_path)
            return jsonify({'error': f'Error processing file: {str(e)}'}), 400

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        return jsonify({'error': 'Upload failed'}), 500

@app.route('/results/<session_id>')
@login_required
def results(session_id):
    """Display analysis results"""
    # Get user info from session
    user_info = {
        'full_name': session.get('username', 'مستخدم'),
        'role': session.get('role', 'user')
    }

    # For now, return a simple results page
    # In a full implementation, this would load the actual analysis results
    return render_template('results.html',
                         user=user_info,
                         session_id=session_id,
                         config={'system_name': 'نظام تحليل قطاع الجمارك'})

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/test_frontend_export.html')
def test_frontend_export():
    """Serve the frontend export test page"""
    return send_from_directory('.', 'test_frontend_export.html')

@app.route('/api/independent-review/export/<format>', methods=['POST'])
@login_required
def export_independent_review(format):
    """Export independent review results with table display accuracy"""
    try:
        # Diagnostic: log which engine is available at request time and process info
        engine_probe, probe_errors = detect_pdf_engine()
        logger.info(f"[EXPORT DIAG] detect_pdf_engine() => {engine_probe}, errors: {probe_errors}")
        import sys, os
        logger.info(f"[EXPORT DIAG] PID={os.getpid()}, python={sys.executable}")

        # Get data from request
        data = request.get_json()
        if not data or 'results' not in data:
            return jsonify({'error': 'No results data provided'}), 400

        results = data['results']
        field_analysis = results.get('field_analysis', [])
        quality_metrics = results.get('quality_metrics', {})

        # Get language preference from request (default to Arabic)
        language = data.get('language', 'ar')

        # Get table display settings for accurate export
        table_settings = data.get('tableSettings', {})

        if format.lower() == 'pdf':
            engine, errors = detect_pdf_engine()
            if engine == 'playwright':
                return export_pdf_playwright_table(field_analysis, quality_metrics, language, table_settings)
            if engine == 'weasyprint':
                return export_webpage_pdf_table(field_analysis, quality_metrics, language, table_settings)
            if engine == 'reportlab':
                return export_pdf_table(field_analysis, quality_metrics, language, table_settings)
            # No available PDF engines - return detailed diagnostic to help user
            return jsonify({
                'error': 'No PDF exporters are available on the server',
                'detected_errors': errors,
                'hint': 'Install Playwright (pip install playwright && playwright install chromium) or ReportLab (pip install reportlab arabic-reshaper python-bidi)'
            }), 500
        elif format.lower() == 'excel':
            return export_excel_table(field_analysis, quality_metrics, table_settings)
        elif format.lower() == 'csv':
            return export_csv(field_analysis, quality_metrics)
        elif format.lower() == 'json':
            return export_json(field_analysis, quality_metrics)
        elif format.lower() == 'webpage':
            return export_webpage_html_response(field_analysis, quality_metrics, language)
        else:
            return jsonify({'error': 'Unsupported export format'}), 400

    except Exception as e:
        # Log full traceback for debugging
        logger.exception(f"Export error: {str(e)}")
        if app.debug:
            return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500
        return jsonify({'error': str(e)}), 500

@app.route('/api/independent-review/export/comprehensive-pdf', methods=['POST'])
@login_required
def export_comprehensive_report():
    """Export comprehensive executive report for decision makers"""
    try:
        data = request.get_json()
        if not data or 'results' not in data:
            return jsonify({'error': 'No results data provided'}), 400

        results = data['results']
        language = data.get('language', 'ar')
        report_type = data.get('reportType', 'executive_summary')
        include_charts = data.get('includeCharts', True)
        include_metrics = data.get('includeMetrics', True)
        include_recommendations = data.get('includeRecommendations', True)
        target_audience = data.get('targetAudience', 'decision_maker')

        # Generate comprehensive PDF report
        return export_comprehensive_pdf_report(
            results,
            language,
            report_type,
            include_charts,
            include_metrics,
            include_recommendations,
            target_audience
        )

    except Exception as e:
        logger.error(f"Comprehensive report export error: {str(e)}")
        return jsonify({'error': f'Comprehensive report export failed: {str(e)}'}), 500


@app.route('/api/independent-review/export/duplicates/<field_name>', methods=['POST'])
@login_required
def export_duplicates(field_name):
    """Export duplicate values for a specific field to Excel"""
    try:
        if not XLSXWRITER_AVAILABLE:
            return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

        # Get data from request - handle both JSON and FormData
        duplicate_details = None
        detailed_duplicates_info = None
        export_type = request.form.get('export_type', 'summary')  # 'summary' or 'full_rows'

        if request.is_json:
            data = request.get_json()
            duplicate_details = data.get('duplicate_details') if data else None
            detailed_duplicates_info = data.get('detailed_duplicates_info')
            export_type = data.get('export_type', 'summary')
        else:
            # Handle FormData
            duplicate_details_json = request.form.get('duplicate_details')
            detailed_duplicates_info_json = request.form.get('detailed_duplicates_info')
            export_type = request.form.get('export_type', 'summary')

            if duplicate_details_json:
                try:
                    duplicate_details = json.loads(duplicate_details_json)
                except json.JSONDecodeError:
                    return jsonify({'error': 'Invalid duplicate details format'}), 400

            if detailed_duplicates_info_json:
                try:
                    detailed_duplicates_info = json.loads(detailed_duplicates_info_json)
                except json.JSONDecodeError:
                    logger.warning("Could not parse detailed duplicates info")

        if not duplicate_details and not detailed_duplicates_info:
            return jsonify({'error': 'No duplicate details provided'}), 400

        from io import BytesIO
        buffer = BytesIO()

        # Create Excel workbook
        workbook = xlsxwriter.Workbook(buffer)

        if export_type == 'full_rows' and detailed_duplicates_info:
            # Export full rows with all data for duplicate values
            worksheet = workbook.add_worksheet('الصفوف المتكررة الكاملة')

            # Formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4BACC6',
                'font_color': 'white',
                'border': 1,
                'text_wrap': True
            })

            data_format = workbook.add_format({
                'border': 1,
                'text_wrap': True
            })

            duplicate_value_format = workbook.add_format({
                'bg_color': '#FFF2CC',
                'border': 1,
                'text_wrap': True,
                'bold': True
            })

            # Collect all unique columns from all duplicate groups
            all_columns = set()
            for dup_info in detailed_duplicates_info.values():
                if 'all_columns' in dup_info:
                    all_columns.update(dup_info['all_columns'])

            all_columns = sorted(list(all_columns))
            if field_name not in all_columns:
                all_columns.insert(0, field_name)

            # Write headers
            for col_idx, col_name in enumerate(all_columns):
                worksheet.write(0, col_idx, str(col_name), header_format)

            # Write data
            row_idx = 1
            for dup_value, dup_info in detailed_duplicates_info.items():
                if 'rows_data' in dup_info:
                    for row_data in dup_info['rows_data']:
                        for col_idx, col_name in enumerate(all_columns):
                            value = row_data.get(col_name, '')
                            # Highlight the duplicate field
                            if col_name == field_name:
                                worksheet.write(row_idx, col_idx, str(value), duplicate_value_format)
                            else:
                                worksheet.write(row_idx, col_idx, str(value), data_format)
                        row_idx += 1

                    # Add separator row
                    for col_idx in range(len(all_columns)):
                        worksheet.write(row_idx, col_idx, '', data_format)
                    row_idx += 1

            # Auto-adjust column widths
            for col_idx, col_name in enumerate(all_columns):
                max_width = max(len(str(col_name)), 10)  # Minimum width of 10
                worksheet.set_column(col_idx, col_idx, min(max_width, 50))  # Max width of 50

        else:
            # Export summary of duplicate values (original functionality)
            worksheet = workbook.add_worksheet('البيانات المتكررة')

            # Formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4BACC6',
                'font_color': 'white',
                'border': 1
            })

            data_format = workbook.add_format({
                'border': 1,
                'text_wrap': True
            })

            # Write headers
            worksheet.write(0, 0, 'القيمة المتكررة', header_format)
            worksheet.write(0, 1, 'عدد التكرار', header_format)
            worksheet.write(0, 2, 'النسبة المئوية', header_format)

            # Write duplicate data
            row = 1
            total_duplicates = sum(duplicate_details.values()) if duplicate_details else 0
            for value, count in duplicate_details.items():
                percentage = (count / total_duplicates * 100) if total_duplicates > 0 else 0
                worksheet.write(row, 0, str(value), data_format)
                worksheet.write(row, 1, int(count), data_format)
                worksheet.write(row, 2, f"{percentage:.2f}%", data_format)
                row += 1

            # Auto-adjust column widths
            worksheet.set_column(0, 0, 40)  # Value column
            worksheet.set_column(1, 1, 15)  # Count column
            worksheet.set_column(2, 2, 15)  # Percentage column

        workbook.close()

        buffer.seek(0)
        excel_data = buffer.getvalue()
        buffer.close()

        # Return Excel as response
        excel_buffer = BytesIO(excel_data)
        excel_buffer.seek(0)

        # Determine filename based on export type
        if export_type == 'full_rows':
            filename = f'صفوف_متكررة_كاملة_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        else:
            filename = f'البيانات_المتكررة_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"Duplicate export error: {str(e)}")
        return jsonify({'error': f'Duplicate export failed: {str(e)}'}), 500

@app.route('/api/independent-review/export/status', methods=['GET'])
@login_required
def export_status():
    """Return which exporters are available on this server"""
    return jsonify({
        'playwright': PLAYWRIGHT_AVAILABLE,
        'weasyprint': WEASYPRINT_AVAILABLE,
        'reportlab': REPORTLAB_AVAILABLE,
        'xlsxwriter': XLSXWRITER_AVAILABLE
    })

@app.route('/api/independent-review/export/indicator-details/', methods=['POST'])
@login_required
def export_indicator_details():
    """Export indicator details as Excel"""
    try:
        if not XLSXWRITER_AVAILABLE:
            return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

        data = request.get_json()
        if not data or 'indicator_details' not in data:
            return jsonify({'error': 'No indicator details provided'}), 400

        indicator_details = data['indicator_details']

        from io import BytesIO
        buffer = BytesIO()
        workbook = xlsxwriter.Workbook(buffer)
        worksheet = workbook.add_worksheet('تفاصيل المؤشر')

        header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
        data_format = workbook.add_format({'border': 1, 'text_wrap': True})

        # Write summary information
        if 'summary' in indicator_details:
            summary = indicator_details['summary']
            worksheet.write(0, 0, 'ملخص المؤشر', header_format)
            worksheet.write(1, 0, f"المؤشر: {summary.get('indicator', '')}", data_format)
            worksheet.write(2, 0, f"القيمة: {summary.get('total_input_gaps', summary.get('total_irregular_repetition', summary.get('total_problematic_fields', 0)))}", data_format)
            worksheet.write(3, 0, f"الحقول المتأثرة: {summary.get('affected_fields', 0)}", data_format)
            worksheet.write(4, 0, f"تاريخ التصدير: {summary.get('export_timestamp', '')}", data_format)

        # Write field details
        if 'field_details' in indicator_details and indicator_details['field_details']:
            worksheet.write(6, 0, 'تفاصيل الحقول', header_format)
            headers = ['اسم الحقل', 'القيم الناقصة', 'القيم الشاذة', 'القيم المكررة', 'النسبة المئوية', 'مستوى المخاطر']
            for col, header in enumerate(headers):
                worksheet.write(7, col, header, header_format)

            row = 8
            for field in indicator_details['field_details']:
                worksheet.write(row, 0, field.get('field_name', ''), data_format)
                worksheet.write(row, 1, field.get('null_values', field.get('duplicate_values', 0)), data_format)
                worksheet.write(row, 2, field.get('outlier_values', 0), data_format)
                worksheet.write(row, 3, field.get('duplicate_values', 0), data_format)
                worksheet.write(row, 4, field.get('percentage', '0%'), data_format)
                worksheet.write(row, 5, field.get('risk_level', 'منخفض'), data_format)
                row += 1

        workbook.close()
        buffer.seek(0)
        excel_data = buffer.getvalue()
        buffer.close()

        excel_buffer = BytesIO(excel_data)
        excel_buffer.seek(0)

        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'indicator_details_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        logger.error(f"Indicator details export error: {str(e)}")
        return jsonify({'error': f'Indicator details export failed: {str(e)}'}), 500

@app.route('/api/independent-review/export/excel', methods=['POST'])
@login_required
def export_duplicate_details_excel():
    """Export duplicate details for a specific field to Excel with full row data"""
    try:
        if not XLSXWRITER_AVAILABLE:
            return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

        # Get data from request
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        language = data.get('language', 'ar')
        exportType = data.get('exportType', 'duplicate_details')
        fieldName = data.get('fieldName', 'Unknown Field')
        duplicate_details = data.get('duplicate_details', {})
        detailed_duplicates_info = data.get('detailed_duplicates_info', {})

        from io import BytesIO
        buffer = BytesIO()

        # Create Excel workbook
        workbook = xlsxwriter.Workbook(buffer)

        if exportType == 'full_duplicate_rows' and detailed_duplicates_info:
            # Export complete rows for all duplicate values
            worksheet = workbook.add_worksheet('الصفوف المتكررة الكاملة')

            # Formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4BACC6',
                'font_color': 'white',
                'border': 1,
                'text_wrap': True
            })

            data_format = workbook.add_format({
                'border': 1,
                'text_wrap': True
            })

            duplicate_highlight_format = workbook.add_format({
                'bg_color': '#FFF2CC',
                'border': 1,
                'text_wrap': True,
                'bold': True
            })

            # Collect all columns
            all_columns = set()
            for dup_value, dup_info in detailed_duplicates_info.items():
                if 'all_columns' in dup_info:
                    all_columns.update(dup_info['all_columns'])

            all_columns = sorted(list(all_columns))
            if fieldName not in all_columns:
                all_columns.insert(0, fieldName)

            # Write title
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 14,
                'align': 'center',
                'bg_color': '#E6F3FF',
                'border': 1
            })
            worksheet.merge_range(0, 0, 0, len(all_columns)-1, f'جميع الصفوف المتكررة - حقل: {fieldName}', title_format)

            # Write headers
            for col_idx, col_name in enumerate(all_columns):
                worksheet.write(2, col_idx, str(col_name), header_format)

            # Write data
            row_idx = 3
            for dup_value, dup_info in detailed_duplicates_info.items():
                if 'rows_data' in dup_info and dup_info['rows_data']:
                    # Write group header
                    group_header_format = workbook.add_format({
                        'bold': True,
                        'bg_color': '#DDEBF7',
                        'border': 1,
                        'text_wrap': True
                    })
                    worksheet.merge_range(row_idx, 0, row_idx, len(all_columns)-1,
                                        f'القيمة المتكررة: {dup_value} (عدد التكرار: {dup_info["count"]})',
                                        group_header_format)
                    row_idx += 1

                    # Write all rows for this duplicate value
                    for row_data in dup_info['rows_data']:
                        for col_idx, col_name in enumerate(all_columns):
                            value = row_data.get(col_name, '')
                            # Highlight the duplicate field column
                            if col_name == fieldName:
                                worksheet.write(row_idx, col_idx, str(value), duplicate_highlight_format)
                            else:
                                worksheet.write(row_idx, col_idx, str(value), data_format)
                        row_idx += 1

                    # Add blank row between groups
                    row_idx += 1

            # Auto-adjust column widths
            for col_idx, col_name in enumerate(all_columns):
                max_width = max(len(str(col_name)), 15)  # Minimum width
                worksheet.set_column(col_idx, col_idx, min(max_width, 50))  # Max width of 50

        else:
            # Original duplicate details export
            worksheet = workbook.add_worksheet('تفاصيل القيم المتكررة')

            # Formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4BACC6',
                'font_color': 'white',
                'border': 1,
                'text_wrap': True
            })

            data_format = workbook.add_format({
                'border': 1,
                'text_wrap': True
            })

            # Write title
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 14,
                'align': 'center',
                'bg_color': '#E6F3FF',
                'border': 1
            })
            worksheet.merge_range(0, 0, 0, 3, f'تفاصيل القيم المتكررة - حقل: {fieldName}', title_format)

            # Write headers
            worksheet.write(2, 0, 'القيمة المتكررة', header_format)
            worksheet.write(2, 1, 'عدد التكرار', header_format)
            worksheet.write(2, 2, 'النسبة المئوية', header_format)
            worksheet.write(2, 3, 'ملاحظات', header_format)

            # Write data
            row = 3
            total_duplicates = sum(int(count) for count in duplicate_details.values()) if duplicate_details else 0

            for value, count in duplicate_details.items():
                percentage = (int(count) / total_duplicates * 100) if total_duplicates > 0 else 0
                notes = get_duplicate_notes(value, count, percentage)

                worksheet.write(row, 0, str(value), data_format)
                worksheet.write(row, 1, int(count), data_format)
                worksheet.write(row, 2, f"{percentage:.2f}%", data_format)
                worksheet.write(row, 3, notes, data_format)
                row += 1

            # Auto-adjust column widths
            worksheet.set_column(0, 0, 40)  # Value column
            worksheet.set_column(1, 1, 15)  # Count column
            worksheet.set_column(2, 2, 15)  # Percentage column
            worksheet.set_column(3, 3, 30)  # Notes column

        workbook.close()

        buffer.seek(0)
        excel_data = buffer.getvalue()
        buffer.close()

        # Return Excel as response
        excel_buffer = BytesIO(excel_data)
        excel_buffer.seek(0)

        # Determine filename based on export type
        if exportType == 'full_duplicate_rows':
            filename = f'جميع_الصفوف_المتكررة_{fieldName}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        else:
            filename = f'تفاصيل_القيم_المتكررة_{fieldName}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"Duplicate details Excel export error: {str(e)}")
        return jsonify({'error': f'Duplicate details Excel export failed: {str(e)}'}), 500

def get_duplicate_notes(value, count, percentage):
    """Generate notes for duplicate values"""
    notes = []
    if count > 100:
        notes.append("تكرار عالي جداً")
    elif count > 50:
        notes.append("تكرار مرتفع")
    elif count > 10:
        notes.append("تكرار متوسط")

    if percentage > 20:
        notes.append("يشكل نسبة كبيرة من البيانات")

    if not notes:
        notes.append("تكرار طبيعي")

    return "، ".join(notes)


def detect_pdf_engine():
    """Try to detect an available PDF engine at runtime.
    Returns tuple (engine_name, error_dict) where engine_name is one of
    'playwright', 'weasyprint', 'reportlab', or None. error_dict contains
    any import errors encountered (for debugging).
    """
    errors = {}

    # Prioritize ReportLab for Windows compatibility, then Playwright, then WeasyPrint
    if REPORTLAB_AVAILABLE:
        return 'reportlab', errors
    elif PLAYWRIGHT_AVAILABLE:
        return 'playwright', errors
    elif WEASYPRINT_AVAILABLE:
        return 'weasyprint', errors

    # If none are available, record errors
    if not REPORTLAB_AVAILABLE:
        errors['reportlab'] = 'Not available'
    if not PLAYWRIGHT_AVAILABLE:
        errors['playwright'] = 'Not available'
    if not WEASYPRINT_AVAILABLE:
        errors['weasyprint'] = 'Not available'

    return None, errors

def export_pdf_table(field_analysis, quality_metrics, language='ar', table_settings=None):
    """Export results as PDF with table display accuracy.

    This function imports ReportLab locally at call time so we don't rely on
    the module-level REPORTLAB_AVAILABLE flag (which can be stale if packages
    were installed after the process started). If the imports fail we return
    a helpful diagnostic JSON payload.
    """
    # Import ReportLab modules dynamically so runtime changes to the
    # environment are respected (avoid stale global flags).
    try:
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
    except Exception as imp_err:
        logger.warning(f"Local ReportLab import failed: {imp_err}")
        return jsonify({
            'error': 'PDF export not available - ReportLab not installed',
            'details': str(imp_err),
            'hint': "Install ReportLab and Arabic helpers: pip install reportlab arabic-reshaper python-bidi"
        }), 500

    try:
        from io import BytesIO
        import arabic_reshaper
        from bidi.algorithm import get_display

        buffer = BytesIO()

        # Create PDF document in landscape orientation with larger page size
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), encoding='utf-8')
        elements = []

        # Register Arabic font if available
        try:
            # Try to use a font that supports Arabic
            # First try to find a system Arabic font
            import os
            font_paths = [
                'C:/Windows/Fonts/arial.ttf',  # Windows Arial
                'C:/Windows/Fonts/tahoma.ttf',  # Windows Tahoma
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
                '/System/Library/Fonts/Arial.ttf',  # macOS
            ]

            arabic_font_path = None
            for path in font_paths:
                if os.path.exists(path):
                    arabic_font_path = path
                    break

            if arabic_font_path:
                pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                arabic_font = 'Arabic'
            else:
                # Fallback to built-in fonts that might support Arabic
                arabic_font = 'Helvetica'
        except Exception as e:
            logger.warning(f"Could not register Arabic font: {e}")
            arabic_font = 'Helvetica'

        # Styles
        styles = getSampleStyleSheet()

        # Create custom styles with Arabic font
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=12,
            spaceAfter=20,
            alignment=1,  # Center
            fontName=arabic_font,
            encoding='utf-8'
        )

        # Custom style for Arabic text
        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontSize=8,
            fontName=arabic_font,
            encoding='utf-8',
            alignment=2  # Right alignment for Arabic
        )

        # Arabic text reshaping function
        def arabic_text(text):
            try:
                if isinstance(text, str) and any('\u0600' <= char <= '\u06FF' for char in text):
                    reshaped = arabic_reshaper.reshape(text)
                    return get_display(reshaped)
                return text
            except Exception as e:
                logger.warning(f"Arabic text processing error: {e}")
                return text

        # Language-specific content
        if language == 'en':
            title_text = "Independent Review Report - Text Field Analysis"
            metrics_title_text = "Data Quality Metrics"
            analysis_title_text = "Text Field Analysis"
            headers = ['Field Name', 'Field Type', 'Importance Level', 'Repetition Rules', 'Total Records', 'Null Values', 'Outlier Values', 'Quality']
            metrics_headers = ['Metric', 'Value', 'Percentage']
            metrics_data_content = [
                ['Irregular Repetition', str(quality_metrics.get('irregular_repetition', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('irregular_repetition', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                ['Input Gaps', str(quality_metrics.get('input_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('input_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                ['Word Gaps', str(quality_metrics.get('word_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('word_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                ['Length Irregularities', str(quality_metrics.get('length_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('length_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                ['Sequence Irregularities', str(quality_metrics.get('sequence_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('sequence_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                ['Outlier Values', str(quality_metrics.get('outlier_values', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('outlier_values', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"]
            ]
            table_direction = 'LEFT'
        else:  # Arabic
            title_text = arabic_text("تقرير المراجعة المستقلة - تحليل الحقول النصية")
            metrics_title_text = arabic_text("مؤشرات جودة البيانات")
            analysis_title_text = arabic_text("تحليل الحقول النصية")
            headers = [arabic_text('اسم الحقل'), arabic_text('نوع الحقل'), arabic_text('مستوى الأهمية'), arabic_text('شروط التكرار'), arabic_text('إجمالي السجلات'), arabic_text('القيم الناقصة'), arabic_text('القيم الشاذة'), arabic_text('الجودة')]
            metrics_headers = [arabic_text('المؤشر'), arabic_text('القيمة'), arabic_text('النسبة المئوية')]
            metrics_data_content = [
                [arabic_text('التكرار غير المنطقي'), str(quality_metrics.get('irregular_repetition', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('irregular_repetition', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('نواقص الإدخال'), str(quality_metrics.get('input_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('input_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('نواقص الكلمات'), str(quality_metrics.get('word_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('word_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('اختلالات الخانات'), str(quality_metrics.get('length_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('length_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('اختلالات التسلسل'), str(quality_metrics.get('sequence_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('sequence_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('النتائج الشاذة'), str(quality_metrics.get('outlier_values', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('outlier_values', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"]
            ]
            table_direction = 'RIGHT'

        # Title
        title = Paragraph(title_text, title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Quality Metrics Summary
        metrics_title = Paragraph(metrics_title_text, styles['Heading2'])
        elements.append(metrics_title)
        elements.append(Spacer(1, 12))

        metrics_data = [metrics_headers] + metrics_data_content

        metrics_table = Table(metrics_data)
        metrics_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(metrics_table)
        elements.append(Spacer(1, 15))

        # Field Analysis Table
        analysis_title = Paragraph(analysis_title_text, styles['Heading2'])
        elements.append(analysis_title)
        elements.append(Spacer(1, 8))

        # Table headers
        table_data = [headers]

        # Add field data
        for field in field_analysis:
            if language == 'en':
                row = [
                    field.get('field_name', ''),
                    field.get('field_type', ''),
                    field.get('importance', ''),
                    field.get('repetition_allowed', ''),
                    str(field.get('total_records', 0)),
                    str(field.get('null_values', 0)),
                    str(field.get('outlier_values', 0)),
                    field.get('quality_notes', '')
                ]
            else:  # Arabic
                row = [
                    arabic_text(field.get('field_name', '')),
                    arabic_text(field.get('field_type', '')),
                    arabic_text(field.get('importance', '')),
                    arabic_text(field.get('repetition_allowed', '')),
                    str(field.get('total_records', 0)),
                    str(field.get('null_values', 0)),
                    str(field.get('outlier_values', 0)),
                    arabic_text(field.get('quality_notes', ''))
                ]
            table_data.append(row)

        # Create table with proper text handling
        if len(table_data) > 1:
            analysis_table = Table(table_data)
            # Set table style based on language direction
            if language == 'ar':  # Arabic - right-to-left table
                analysis_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, 0), arabic_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 6),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 4),
                    ('LEFTPADDING', (0, 0), (-1, -1), 4),
                ]))
            else:  # English - left-to-right table
                analysis_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), arabic_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 6),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 4),
                    ('LEFTPADDING', (0, 0), (-1, -1), 4),
                ]))
            elements.append(analysis_table)

        # Build PDF
        doc.build(elements)

        buffer.seek(0)
        pdf_data = buffer.getvalue()
        buffer.close()

        # Return PDF as response
        from flask import send_file
        pdf_buffer = BytesIO(pdf_data)
        pdf_buffer.seek(0)

        return send_file(
            pdf_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'independent_review_table_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except Exception as e:
        logger.error(f"PDF export error: {str(e)}")
        return jsonify({'error': f'PDF export failed: {str(e)}'}), 500

def export_excel_table(field_analysis, quality_metrics, table_settings=None):
    """Export results as Excel with table display accuracy"""
    if not XLSXWRITER_AVAILABLE:
        return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

    try:
        from io import BytesIO
        buffer = BytesIO()

        # Create Excel workbook
        workbook = xlsxwriter.Workbook(buffer)
        worksheet = workbook.add_worksheet('تحليل الحقول')

        # Formats
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4BACC6',
            'font_color': 'white',
            'border': 1
        })

        data_format = workbook.add_format({
            'border': 1,
            'text_wrap': True
        })

        # Write quality metrics
        worksheet.write(0, 0, 'مؤشرات جودة البيانات', header_format)
        metrics = [
            ['التكرار غير المنطقي', quality_metrics.get('irregular_repetition', 0)],
            ['نواقص الإدخال', quality_metrics.get('input_gaps', 0)],
            ['نواقص الكلمات', quality_metrics.get('word_gaps', 0)],
            ['اختلالات الخانات', quality_metrics.get('length_irregularities', 0)],
            ['اختلالات التسلسل', quality_metrics.get('sequence_irregularities', 0)],
            ['النتائج الشاذة', quality_metrics.get('outlier_values', 0)]
        ]

        for i, (metric, value) in enumerate(metrics):
            worksheet.write(i + 1, 0, metric, data_format)
            worksheet.write(i + 1, 1, value, data_format)

        # Write field analysis with all columns from the table
        worksheet.write(9, 0, 'تحليل الحقول النصية', header_format)
        headers = [
            'اسم الحقل', 'نوع الحقل', 'مستوى الأهمية', 'شروط التكرار',
            'إجمالي السجلات (Total Records)', 'عدد القيم الناقصة (Count Missing)', 'عدد القيم الشاذة (Count Invalid)', 'عدد القيم المكررة (Count Duplicates)',
            'عدد القيم الفريدة (Distinct Count)', 'عدد الخانات الثابتة (Min/Max)', 'نمط متوقع (Regex / Format)', 'أمثلة على القيم الشاذة (Sample Bad Values)',
            'ملاحظات الجودة', 'رمز الحالة الآلي (Status Code)', 'التوصية (Remediation)', 'أولوية المعالجة (Priority)',
            'مصدر البيانات (Source)', 'مسؤول البيانات (Data Steward)', 'آخر تحديث (Last Checked)', 'قواعد التحقق المتقاطعة (Cross-field rules)'
        ]

        for col, header in enumerate(headers):
            worksheet.write(10, col, header, header_format)

        # Write field data with all columns from the table
        for row, field in enumerate(field_analysis, start=11):
            null_percentage = min(100.0, max(0.0, (field.get('null_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0
            outlier_percentage = min(100.0, max(0.0, (field.get('outlier_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0

            repetition_desc = get_repetition_description(field)
            sequence_desc = 'نعم (متسلسل)' if field.get('sequence_required') == 'نعم' else 'لا'
            expected_words_desc = field.get('expected_words', 'لا ينطبق') if field.get('expected_words') != 'لا ينطبق' else 'لا ينطبق'
            field_type = get_field_type(field)
            data_source = get_data_source(field)
            regex_pattern = get_regex_pattern(field)
            status_code = get_status_code(field)
            recommendation = get_recommendation(field)
            outlier_examples = get_outlier_examples(field)
            general_notes = generate_general_notes(field)
            total_records = field.get('total_records', 'غير محدد')
            distinct_count = field.get('unique_values', 'غير محدد')
            priority = get_priority(field)
            data_steward = get_data_steward(field)
            last_checked = datetime.now().strftime('%Y-%m-%d')
            cross_field_rules = get_cross_field_rules(field)

            data = [
                field.get('field_name', ''),
                field_type,
                field.get('importance', ''),
                repetition_desc,
                total_records,
                field.get('null_values', 0),
                field.get('outlier_values', 0),
                field.get('duplicates', 0),
                distinct_count,
                field.get('fixed_length', 'غير محدد'),
                regex_pattern,
                outlier_examples,
                general_notes,
                status_code,
                recommendation,
                priority,
                data_source,
                data_steward,
                last_checked,
                cross_field_rules
            ]
            for col, value in enumerate(data):
                worksheet.write(row, col, value, data_format)

        workbook.close()

        buffer.seek(0)
        excel_data = buffer.getvalue()
        buffer.close()

        # Return Excel as response
        excel_buffer = BytesIO(excel_data)
        excel_buffer.seek(0)

        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'independent_review_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        logger.error(f"Excel export error: {str(e)}")
        return jsonify({'error': f'Excel export failed: {str(e)}'}), 500

def export_csv(field_analysis, quality_metrics):
    """Export results as CSV with proper UTF-8 encoding for Arabic text"""
    try:
        import csv
        from io import StringIO

        output = StringIO()

        # Write quality metrics
        writer = csv.writer(output)
        writer.writerow(['مؤشرات جودة البيانات'])
        writer.writerow(['المؤشر', 'القيمة'])
        writer.writerow(['التكرار غير المنطقي', quality_metrics.get('irregular_repetition', 0)])
        writer.writerow(['نواقص الإدخال', quality_metrics.get('input_gaps', 0)])
        writer.writerow(['نواقص الكلمات', quality_metrics.get('word_gaps', 0)])
        writer.writerow(['اختلالات الخانات', quality_metrics.get('length_irregularities', 0)])
        writer.writerow(['اختلالات التسلسل', quality_metrics.get('sequence_irregularities', 0)])
        writer.writerow(['النتائج الشاذة', quality_metrics.get('outlier_values', 0)])
        writer.writerow([])  # Empty row

        # Write field analysis
        writer.writerow(['تحليل الحقول النصية'])
        headers = ['اسم الحقل', 'نوع الحقل', 'مستوى الأهمية', 'شروط التكرار', 'إجمالي السجلات',
                  'القيم الناقصة', 'القيم الشاذة', 'الجودة', 'مصدر البيانات', 'مسؤول البيانات']
        writer.writerow(headers)

        for field in field_analysis:
            row = [
                field.get('field_name', ''),
                field.get('field_type', ''),
                field.get('importance', ''),
                field.get('repetition_allowed', ''),
                field.get('total_records', 0),
                field.get('null_values', 0),
                field.get('outlier_values', 0),
                field.get('quality_notes', ''),
                field.get('data_source', ''),
                field.get('data_steward', '')
            ]
            writer.writerow(row)

        csv_data = output.getvalue()
        output.close()

        # Return CSV as response with proper UTF-8 BOM for Excel compatibility
        from flask import Response
        # Add UTF-8 BOM to ensure Excel opens Arabic text correctly
        csv_data_with_bom = '\ufeff' + csv_data
        return Response(
            csv_data_with_bom,
            mimetype='text/csv; charset=utf-8',
            headers={
                'Content-Disposition': f'attachment; filename=independent_review_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )

    except Exception as e:
        logger.error(f"CSV export error: {str(e)}")
        return jsonify({'error': f'CSV export failed: {str(e)}'}), 500

def export_webpage_html_response(field_analysis, quality_metrics, language='ar'):
    """Export the exact same webpage HTML as displayed in the browser"""
    try:
        # Generate HTML content that exactly matches the webpage
        html_content = generate_webpage_html(field_analysis, quality_metrics, language)

        # Return HTML as downloadable file
        from flask import Response
        return Response(
            html_content,
            mimetype='text/html; charset=utf-8',
            headers={'Content-Disposition': f'attachment; filename=independent_review_webpage_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html'}
        )

    except Exception as e:
        logger.error(f"Web page HTML export error: {str(e)}")
        return jsonify({'error': f'Web page HTML export failed: {str(e)}'}), 500

def export_webpage_pdf(field_analysis, quality_metrics, language='ar'):
    """Export webpage as PDF using WeasyPrint with optimized layout for all data visibility"""
    if not WEASYPRINT_AVAILABLE:
        return jsonify({'error': 'Web page PDF export not available - WeasyPrint not installed'}), 500

    try:
        # Generate HTML content for the webpage with PDF-specific optimizations
        html_content = generate_webpage_html_pdf(field_analysis, quality_metrics, language)

        # Create PDF from HTML with optimized settings
        from io import BytesIO
        pdf_buffer = BytesIO()

        # Convert HTML to PDF with landscape orientation and custom page size for better data visibility
        try:
            weasyprint.HTML(string=html_content).write_pdf(
                pdf_buffer,
                stylesheets=[weasyprint.CSS(string="""
                @page {
                    size: A2 landscape;
                    margin: 0.3cm;
                    -weasy-page-break-inside: avoid;
                }
                body {
                    font-size: 6px !important;
                    line-height: 1.0 !important;
                }
                .table th, .table td {
                    padding: 1px 2px !important;
                    font-size: 5px !important;
                    line-height: 1.0 !important;
                }
                .metric-card {
                    margin-bottom: 0.3rem !important;
                    padding: 0.3rem !important;
                }
                .metric-card h4 {
                    font-size: 0.8rem !important;
                    margin-bottom: 0.1rem !important;
                }
                .metric-card p {
                    font-size: 0.5rem !important;
                    margin-bottom: 0.05rem !important;
                }
                .card-header h3 {
                    font-size: 0.8rem !important;
                }
                .badge {
                    font-size: 0.4rem !important;
                    padding: 0.1rem 0.2rem !important;
                }
                .table-responsive {
                    overflow: visible !important;
                }
                .table {
                    font-size: 4px !important;
                    width: 100% !important;
                    table-layout: fixed !important;
                }
                .table th {
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    writing-mode: horizontal-tb !important;
                    transform: none !important;
                }
                .table td {
                    word-wrap: break-word !important;
                    white-space: normal !important;
                    overflow-wrap: break-word !important;
                }
                .small {
                    font-size: 3px !important;
                }
                .text-muted, .text-danger, .text-success, .text-warning, .text-info {
                    font-size: 4px !important;
                }
            """)]
        )
        except OSError as pdf_error:
            logger.error(f"WeasyPrint library load error: {pdf_error}")
            return jsonify({
                'error': 'Web page PDF export failed: WeasyPrint runtime dependencies are missing',
                'details': str(pdf_error)
            }), 500

        pdf_buffer.seek(0)
        pdf_data = pdf_buffer.getvalue()
        pdf_buffer.close()

        # Return PDF as response
        pdf_response_buffer = BytesIO(pdf_data)
        pdf_response_buffer.seek(0)

        return send_file(
            pdf_response_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'independent_review_webpage_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except Exception as e:
        logger.error(f"Web page PDF export error: {str(e)}")
        return jsonify({'error': f'Web page PDF export failed: {str(e)}'}), 500


def export_pdf_playwright_table(field_analysis, quality_metrics, language='ar', table_settings=None):
    """Export webpage as PDF using Playwright with table display accuracy"""
    if not PLAYWRIGHT_AVAILABLE:
        return jsonify({'error': 'Playwright not available'}), 500

    try:
        # Generate the HTML content optimized for PDF with table settings
        html_content = generate_webpage_html_pdf_table(field_analysis, quality_metrics, language, table_settings)

        # Use playwright to render HTML and export to PDF with optimized settings
        from io import BytesIO

        with sync_playwright() as pw:
            browser = pw.chromium.launch()
            page = browser.new_page()

            # Set viewport to ensure all content fits
            page.set_viewport_size({"width": 2560, "height": 1440})

            # Set the content and wait for network idle to ensure assets load
            page.set_content(html_content, wait_until='networkidle')

            # Generate PDF with landscape orientation and A2 size for maximum data visibility
            pdf_bytes = page.pdf(
                format='A2',
                landscape=True,
                print_background=True,
                margin={'top': '0.3cm', 'right': '0.3cm', 'bottom': '0.3cm', 'left': '0.3cm'},
                prefer_css_page_size=False
            )
            browser.close()

        pdf_buffer = BytesIO(pdf_bytes)
        pdf_buffer.seek(0)
        return send_file(
            pdf_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'independent_review_table_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except Exception as e:
        logger.error(f"Playwright PDF export error: {e}")
        return jsonify({'error': f'Playwright PDF export failed: {str(e)}'}), 500

def generate_webpage_html_pdf_table(field_analysis, quality_metrics, language='ar', table_settings=None):
    """Generate HTML content optimized for PDF export with table display accuracy"""
    if language == 'en':
        title = "Independent Review Report - Text Field Analysis"
        metrics_title = "Data Quality Metrics"
        analysis_title = "Text Field Analysis"
        direction = 'ltr'
        text_align = 'left'
    else:
        title = "تقرير المراجعة المستقلة - تحليل الحقول النصية"
        metrics_title = "مؤشرات جودة البيانات"
        analysis_title = "تحليل الحقول النصية"
        direction = 'rtl'
        text_align = 'right'

    # Get the exact table headers from the webpage
    if language == 'en':
        headers = [
            'Field Name', 'Field Type', 'Importance Level', 'Repetition Rules',
            'Total Records', 'Count Missing', 'Count Invalid', 'Count Duplicates',
            'Distinct Count', 'Min/Max', 'Regex / Format', 'Sample Bad Values',
            'Quality Notes', 'Status Code', 'Remediation', 'Priority',
            'Source', 'Data Steward', 'Last Checked', 'Cross-field rules'
        ]
    else:
        headers = [
            'اسم الحقل', 'نوع الحقل', 'مستوى الأهمية', 'شروط التكرار',
            'إجمالي السجلات (Total Records)', 'عدد القيم الناقصة (Count Missing)', 'عدد القيم الشاذة (Count Invalid)', 'عدد القيم المكررة (Count Duplicates)',
            'عدد القيم الفريدة (Distinct Count)', 'عدد الخانات الثابتة (Min/Max)', 'نمط متوقع (Regex / Format)', 'أمثلة على القيم الشاذة (Sample Bad Values)',
            'ملاحظات الجودة', 'رمز الحالة الآلي (Status Code)', 'التوصية (Remediation)', 'أولوية المعالجة (Priority)',
            'مصدر البيانات (Source)', 'مسؤول البيانات (Data Steward)', 'آخر تحديث (Last Checked)', 'قواعد التحقق المتقاطعة (Cross-field rules)'
        ]

    html = f"""
    <!DOCTYPE html>
    <html lang="{language}" dir="{direction}">
    <head>
        <meta charset="UTF-8">
        <title>{title}</title>
        <style>
            body {{
                font-family: 'DejaVu Sans', 'Arial Unicode MS', Arial, sans-serif;
                direction: {direction};
                background: white;
                margin: 0;
                padding: 5px;
                font-size: 4px;
                line-height: 1.0;
            }}
            .container-fluid {{
                max-width: none;
                padding: 0;
            }}
            .card {{
                border: 1px solid #000;
                border-radius: 1px;
                margin-bottom: 3px;
                page-break-inside: avoid;
            }}
            .card-header {{
                background: #343a40;
                color: white;
                border-bottom: 1px solid #000;
                padding: 2px 3px;
                border-radius: 1px 1px 0 0;
            }}
            .card-header h3 {{
                margin: 0;
                font-size: 6px;
                font-weight: bold;
            }}
            .card-body {{
                padding: 3px;
            }}
            .table {{
                margin-bottom: 0;
                border-collapse: collapse;
                width: 100%;
                font-size: 3px;
                table-layout: fixed;
            }}
            .table th {{
                background: #343a40 !important;
                color: white !important;
                border: 1px solid #000;
                padding: 1px 1px;
                font-weight: bold;
                text-align: {text_align};
                vertical-align: middle;
                font-size: 3px;
                white-space: normal;
                word-wrap: break-word;
            }}
            .table td {{
                border: 1px solid #000;
                padding: 1px 1px;
                text-align: {text_align};
                vertical-align: middle;
                font-size: 2px;
                line-height: 1.0;
                word-wrap: break-word;
                white-space: normal;
            }}
            .table-striped tbody tr:nth-of-type(odd) {{
                background-color: #f8f9fa;
            }}
            .badge {{
                font-size: 2px;
                padding: 0.5px 1px;
                border-radius: 1px;
                display: inline-block;
                white-space: nowrap;
            }}
            .quality-badge {{
                font-size: 2px;
                padding: 0.5px 1px;
                border-radius: 1px;
            }}
            .text-center {{
                text-align: center !important;
            }}
            .text-danger {{
                color: #dc3545 !important;
            }}
            .text-success {{
                color: #28a745 !important;
            }}
            .text-warning {{
                color: #ffc107 !important;
            }}
            .text-info {{
                color: #17a2b8 !important;
            }}
            .text-muted {{
                color: #6c757d !important;
            }}
            .small {{
                font-size: 2px;
            }}
            .metric-card {{
                background: #667eea;
                color: white;
                border-radius: 1px;
                padding: 1px;
                margin-bottom: 1px;
                text-align: center;
                border: 1px solid #000;
            }}
            .metric-card h4 {{
                font-size: 3px;
                margin-bottom: 0.5px;
                font-weight: bold;
            }}
            .metric-card p {{
                font-size: 2px;
                margin-bottom: 0.2px;
            }}
            .row {{
                display: table;
                width: 100%;
                table-layout: fixed;
            }}
            .col-md-4 {{
                display: table-cell;
                width: 33.333%;
                vertical-align: top;
                padding: 0 1px;
            }}
            .mb-4 {{
                margin-bottom: 3px !important;
            }}
            .table-responsive {{
                overflow: visible !important;
            }}
            .fa {{
                display: none;
            }}
            @media print {{
                body {{
                    background: white !important;
                }}
                .card {{
                    box-shadow: none !important;
                    border: 1px solid #000 !important;
                }}
                .table {{
                    page-break-inside: avoid;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>{title}</h3>
                        </div>
                        <div class="card-body">
                            <!-- Quality Metrics Cards -->
                            <div class="row mb-4">
    """

    # Add quality metrics cards (same as webpage)
    metric_cards = [
        {'key': 'irregular_repetition', 'title': 'التكرار غير المنطقي' if language != 'en' else 'Irregular Repetition', 'icon': 'fas fa-copy', 'color': 'warning'},
        {'key': 'input_gaps', 'title': 'قائمة نواقص الإدخال' if language != 'en' else 'Input Gaps', 'icon': 'fas fa-exclamation-triangle', 'color': 'danger'},
        {'key': 'word_gaps', 'title': 'قائمة نواقص الكلمات' if language != 'en' else 'Word Gaps', 'icon': 'fas fa-font', 'color': 'warning'},
        {'key': 'length_irregularities', 'title': 'قائمة اختلالات الخانات' if language != 'en' else 'Length Irregularities', 'icon': 'fas fa-ruler', 'color': 'info'},
        {'key': 'sequence_irregularities', 'title': 'قائمة اختلالات التسلسل' if language != 'en' else 'Sequence Irregularities', 'icon': 'fas fa-sort-numeric-up', 'color': 'secondary'},
        {'key': 'outlier_values', 'title': 'قائمة النتائج الشاذة' if language != 'en' else 'Outlier Values', 'icon': 'fas fa-outdent', 'color': 'dark'}
    ]

    for card in metric_cards:
        value = quality_metrics.get(card['key'], 0)
        percentage = f"{quality_metrics.get('total', 0) and (value / quality_metrics.get('total', 1)) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%"
        html += f"""
                                <div class="col-md-4">
                                    <div class="metric-card" style="background: {get_card_color(card['color'])}">
                                        <h4>{value}</h4>
                                        <p>{card['title']}</p>
                                        <small>{percentage} من الحقول</small>
                                    </div>
                                </div>
        """

    html += f"""
                            </div>

                            <!-- Field Analysis Table -->
                            <div class="card">
                                <div class="card-header">
                                    <h3>{analysis_title}</h3>
                                </div>
                                <div class="card-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
    """

    # Add table headers
    for header in headers:
        html += f"<th>{header}</th>"

    html += """
                                            </tr>
                                        </thead>
                                        <tbody>
    """

    # Add table rows (exact same format as webpage)
    for field in field_analysis:
        null_percentage = min(100.0, max(0.0, (field.get('null_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0
        outlier_percentage = min(100.0, max(0.0, (field.get('outlier_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0

        repetition_desc = get_repetition_description(field)
        sequence_desc = 'نعم (متسلسل)' if field.get('sequence_required') == 'نعم' else 'لا'
        expected_words_desc = field.get('expected_words', 'لا ينطبق') if field.get('expected_words') != 'لا ينطبق' else 'لا ينطبق'
        field_type = get_field_type(field)
        data_source = get_data_source(field)
        regex_pattern = get_regex_pattern(field)
        status_code = get_status_code(field)
        recommendation = get_recommendation(field)
        outlier_examples = get_outlier_examples(field)
        general_notes = generate_general_notes(field)
        total_records = field.get('total_records', 'غير محدد')
        distinct_count = field.get('unique_values', 'غير محدد')
        priority = get_priority(field)
        data_steward = get_data_steward(field)
        last_checked = datetime.now().strftime('%Y-%m-%d')
        cross_field_rules = get_cross_field_rules(field)

        html += f"""
                                            <tr>
                                                <td><strong>{field.get('field_name', '')}</strong></td>
                                                <td><span class="badge bg-info">{field_type}</span></td>
                                                <td><span class="badge bg-primary">{field.get('importance', '')}</span></td>
                                                <td>{repetition_desc}</td>
                                                <td><span class="badge bg-light text-dark">{total_records}</span></td>
                                                <td><span class="badge bg-danger">{field.get('null_values', 0)}</span></td>
                                                <td><span class="badge bg-warning">{null_percentage:.1f}%</span></td>
                                                <td><span class="badge bg-danger">{field.get('outlier_values', 0)}</span></td>
                                                <td><span class="badge bg-danger">{outlier_percentage:.1f}%</span></td>
                                                <td><span class="badge bg-info">{field.get('duplicates', 0)}</span></td>
                                                <td><span class="badge bg-success">{distinct_count}</span></td>
                                                <td>{field.get('fixed_length', 'غير محدد')}</td>
                                                <td><code class="text-muted small">{regex_pattern}</code></td>
                                                <td><small class="text-danger">{outlier_examples}</small></td>
                                                <td>
                                                    <span class="quality-badge {get_quality_badge_class(general_notes)}">
                                                        {general_notes}
                                                    </span>
                                                </td>
                                                <td><span class="badge {get_status_badge_class(status_code)}">{status_code}</span></td>
                                                <td><small class="text-muted">{recommendation}</small></td>
                                                <td><span class="badge {get_priority_badge_class(priority)}">{priority}</span></td>
                                                <td><span class="badge bg-secondary">{data_source}</span></td>
                                                <td><small class="text-info">{data_steward}</small></td>
                                                <td><small class="text-muted">{last_checked}</small></td>
                                                <td><small class="text-primary">{cross_field_rules}</small></td>
                                            </tr>
        """

    html += """
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return html

def generate_webpage_html(field_analysis, quality_metrics, language='ar'):
    """Generate HTML content that exactly matches the webpage table design"""
    if language == 'en':
        title = "Independent Review Report - Text Field Analysis"
        metrics_title = "Data Quality Metrics"
        analysis_title = "Text Field Analysis"
        direction = 'ltr'
        text_align = 'left'
    else:
        title = "تقرير المراجعة المستقلة - تحليل الحقول النصية"
        metrics_title = "مؤشرات جودة البيانات"
        analysis_title = "تحليل الحقول النصية"
        direction = 'rtl'
        text_align = 'right'

    # Get the exact table headers from the webpage
    if language == 'en':
        headers = [
            'Field Name', 'Field Type', 'Importance Level', 'Repetition Rules',
            'Total Records', 'Count Missing', 'Count Invalid', 'Count Duplicates',
            'Distinct Count', 'Min/Max', 'Regex / Format', 'Sample Bad Values',
            'Quality Notes', 'Status Code', 'Remediation', 'Priority',
            'Source', 'Data Steward', 'Last Checked', 'Cross-field rules'
        ]
    else:
        headers = [
            'اسم الحقل', 'نوع الحقل', 'مستوى الأهمية', 'شروط التكرار',
            'إجمالي السجلات (Total Records)', 'عدد القيم الناقصة (Count Missing)', 'عدد القيم الشاذة (Count Invalid)', 'عدد القيم المكررة (Count Duplicates)',
            'عدد القيم الفريدة (Distinct Count)', 'عدد الخانات الثابتة (Min/Max)', 'نمط متوقع (Regex / Format)', 'أمثلة على القيم الشاذة (Sample Bad Values)',
            'ملاحظات الجودة', 'رمز الحالة الآلي (Status Code)', 'التوصية (Remediation)', 'أولوية المعالجة (Priority)',
            'مصدر البيانات (Source)', 'مسؤول البيانات (Data Steward)', 'آخر تحديث (Last Checked)', 'قواعد التحقق المتقاطعة (Cross-field rules)'
        ]

    html = f"""
    <!DOCTYPE html>
    <html lang="{language}" dir="{direction}">
    <head>
        <meta charset="UTF-8">
        <title>{title}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: {direction};
                background: #f8f9fa;
                margin: 0;
                padding: 20px;
            }}
            .container-fluid {{
                max-width: none;
                padding: 0;
            }}
            .card {{
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                border: 1px solid rgba(0, 0, 0, 0.125);
                border-radius: 0.375rem;
                margin-bottom: 1.5rem;
            }}
            .card-header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-bottom: 1px solid rgba(0, 0, 0, 0.125);
                padding: 0.75rem 1.25rem;
                border-radius: 0.375rem 0.375rem 0 0;
            }}
            .card-header h3 {{
                margin: 0;
                font-size: 1.25rem;
                font-weight: 500;
            }}
            .card-body {{
                padding: 1.25rem;
            }}
            .table {{
                margin-bottom: 0;
            }}
            .table th {{
                background: #343a40 !important;
                color: white !important;
                border: 1px solid #dee2e6;
                padding: 0.75rem;
                font-weight: 600;
                text-align: {text_align};
                vertical-align: middle;
            }}
            .table td {{
                border: 1px solid #dee2e6;
                padding: 0.75rem;
                text-align: {text_align};
                vertical-align: middle;
            }}
            .table-striped tbody tr:nth-of-type(odd) {{
                background-color: rgba(0, 0, 0, 0.05);
            }}
            .table-hover tbody tr:hover {{
                background-color: rgba(0, 0, 0, 0.075);
                color: #212529;
            }}
            .badge {{
                font-size: 0.75em;
                padding: 0.35em 0.65em;
                border-radius: 0.375rem;
            }}
            .quality-badge {{
                font-size: 0.8em;
                padding: 2px 8px;
                border-radius: 10px;
            }}
            .text-center {{
                text-align: center !important;
            }}
            .text-danger {{
                color: #dc3545 !important;
            }}
            .text-success {{
                color: #28a745 !important;
            }}
            .text-warning {{
                color: #ffc107 !important;
            }}
            .text-info {{
                color: #17a2b8 !important;
            }}
            .text-muted {{
                color: #6c757d !important;
            }}
            .small {{
                font-size: 0.875em;
            }}
            .metric-card {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 0.5rem;
                padding: 1.5rem;
                margin-bottom: 1rem;
                text-align: center;
            }}
            .metric-card.warning {{
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }}
            .metric-card.danger {{
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            }}
            .row {{
                display: flex;
                flex-wrap: wrap;
                margin-right: -15px;
                margin-left: -15px;
            }}
            .col-md-4 {{
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
                padding-right: 15px;
                padding-left: 15px;
            }}
            .mb-4 {{
                margin-bottom: 1.5rem !important;
            }}
            .mb-3 {{
                margin-bottom: 1rem !important;
            }}
            .mt-3 {{
                margin-top: 1rem !important;
            }}
            .table-responsive {{
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }}
            @media print {{
                body {{
                    background: white !important;
                }}
                .card {{
                    box-shadow: none !important;
                    border: 1px solid #000 !important;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-search-plus"></i> {title}
                            </h3>
                            <small class="text-white-50">برمبت مراجعة وتحليل الحقول النصية (المتكامل)</small>
                        </div>
                        <div class="card-body">
                            <!-- Quality Metrics Cards -->
                            <div class="row mb-4">
    """

    # Add quality metrics cards (same as webpage)
    metric_cards = [
        {'key': 'irregular_repetition', 'title': 'التكرار غير المنطقي' if language != 'en' else 'Irregular Repetition', 'icon': 'fas fa-copy', 'color': 'warning'},
        {'key': 'input_gaps', 'title': 'قائمة نواقص الإدخال' if language != 'en' else 'Input Gaps', 'icon': 'fas fa-exclamation-triangle', 'color': 'danger'},
        {'key': 'word_gaps', 'title': 'قائمة نواقص الكلمات' if language != 'en' else 'Word Gaps', 'icon': 'fas fa-font', 'color': 'warning'},
        {'key': 'length_irregularities', 'title': 'قائمة اختلالات الخانات' if language != 'en' else 'Length Irregularities', 'icon': 'fas fa-ruler', 'color': 'info'},
        {'key': 'sequence_irregularities', 'title': 'قائمة اختلالات التسلسل' if language != 'en' else 'Sequence Irregularities', 'icon': 'fas fa-sort-numeric-up', 'color': 'secondary'},
        {'key': 'outlier_values', 'title': 'قائمة النتائج الشاذة' if language != 'en' else 'Outlier Values', 'icon': 'fas fa-outdent', 'color': 'dark'}
    ]

    for card in metric_cards:
        value = quality_metrics.get(card['key'], 0)
        percentage = f"{quality_metrics.get('total', 0) and (value / quality_metrics.get('total', 1)) * 100:.1f}%" if quality_metrics.get('total', 0) > 0 else "0%"
        html += f"""
                                <div class="col-md-4 mb-3">
                                    <div class="card metric-card {card['color'] if card['color'] in ['warning', 'danger'] else ''}" style="background: {get_card_color(card['color'])}">
                                        <div class="card-body text-center">
                                            <i class="{card['icon']} fa-2x mb-2"></i>
                                            <h4 class="card-title">{value}</h4>
                                            <p class="card-text">{card['title']}</p>
                                            <small class="text-white-50">{percentage} من الحقول</small>
                                        </div>
                                    </div>
                                </div>
        """

    html += f"""
                            </div>

                            <!-- Field Analysis Table -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-table"></i> {analysis_title}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
    """

    # Add table headers
    for header in headers:
        html += f"<th>{header}</th>"

    html += """
                                                </tr>
                                            </thead>
                                            <tbody>
    """

    # Add table rows (exact same format as webpage)
    for field in field_analysis:
        null_percentage = min(100.0, max(0.0, (field.get('null_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0
        outlier_percentage = min(100.0, max(0.0, (field.get('outlier_values', 0) / field.get('total_records', 1)) * 100)) if field.get('total_records', 0) > 0 else 0

        repetition_desc = get_repetition_description(field)
        sequence_desc = 'نعم (متسلسل)' if field.get('sequence_required') == 'نعم' else 'لا'
        expected_words_desc = field.get('expected_words', 'لا ينطبق') if field.get('expected_words') != 'لا ينطبق' else 'لا ينطبق'
        field_type = get_field_type(field)
        data_source = get_data_source(field)
        regex_pattern = get_regex_pattern(field)
        status_code = get_status_code(field)
        recommendation = get_recommendation(field)
        outlier_examples = get_outlier_examples(field)
        general_notes = generate_general_notes(field)
        total_records = field.get('total_records', 'غير محدد')
        distinct_count = field.get('unique_values', 'غير محدد')
        priority = get_priority(field)
        data_steward = get_data_steward(field)
        last_checked = datetime.now().strftime('%Y-%m-%d')
        cross_field_rules = get_cross_field_rules(field)

        html += f"""
                                                <tr>
                                                    <td><strong>{field.get('field_name', '')}</strong></td>
                                                    <td><span class="badge bg-info">{field_type}</span></td>
                                                    <td><span class="badge bg-primary">{field.get('importance', '')}</span></td>
                                                    <td>{repetition_desc}</td>
                                                    <td><span class="badge bg-light text-dark">{total_records}</span></td>
                                                    <td><span class="badge bg-danger">{field.get('null_values', 0)}</span></td>
                                                    <td><span class="badge bg-warning">{null_percentage:.1f}%</span></td>
                                                    <td><span class="badge bg-danger">{field.get('outlier_values', 0)}</span></td>
                                                    <td><span class="badge bg-danger">{outlier_percentage:.1f}%</span></td>
                                                    <td><span class="badge bg-info">{field.get('duplicates', 0)}</span></td>
                                                    <td><span class="badge bg-success">{distinct_count}</span></td>
                                                    <td>{field.get('fixed_length', 'غير محدد')}</td>
                                                    <td><code class="text-muted small">{regex_pattern}</code></td>
                                                    <td><small class="text-danger">{outlier_examples}</small></td>
                                                    <td>
                                                        <span class="quality-badge {get_quality_badge_class(general_notes)}">
                                                            {general_notes}
                                                        </span>
                                                    </td>
                                                    <td><span class="badge {get_status_badge_class(status_code)}">{status_code}</span></td>
                                                    <td><small class="text-muted">{recommendation}</small></td>
                                                    <td><span class="badge {get_priority_badge_class(priority)}">{priority}</span></td>
                                                    <td><span class="badge bg-secondary">{data_source}</span></td>
                                                    <td><small class="text-info">{data_steward}</small></td>
                                                    <td><small class="text-muted">{last_checked}</small></td>
                                                    <td><small class="text-primary">{cross_field_rules}</small></td>
                                                </tr>
        """

    html += """
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return html

# Helper functions for webpage HTML generation
def get_card_color(color):
    colors = {
        'primary': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'warning': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'danger': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
        'info': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'secondary': 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
        'dark': 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
    }
    return colors.get(color, colors['primary'])

def get_repetition_description(field):
    return field.get('repetition_allowed', 'لا')

def get_field_type(field):
    return field.get('field_type', 'نصي')

def get_data_source(field):
    return field.get('data_source', 'قاعدة البيانات')

def get_regex_pattern(field):
    return field.get('regex_pattern', 'غير محدد')

def get_status_code(field):
    return field.get('status_code', 'OK')

def get_recommendation(field):
    return field.get('recommendation', 'لا توجد توصيات')

def get_outlier_examples(field):
    return field.get('outlier_examples', 'لا توجد أمثلة')

def generate_general_notes(field):
    return field.get('quality_notes', 'جودة جيدة')

def get_priority(field):
    return field.get('priority', 'متوسطة')

def get_data_steward(field):
    return field.get('data_steward', 'غير محدد')

def get_cross_field_rules(field):
    return field.get('cross_field_rules', 'لا توجد قواعد')

def format_duplicate_details_for_table(field_name, duplicates, duplicate_details, df=None, show_detailed_view=True):
    """Format duplicate details for table display with comprehensive preview and export options"""
    import html
    if duplicates == 0:
        return f'<div class="text-center"><span class="badge bg-success fs-6 px-2 py-1"><i class="fas fa-check-circle me-1"></i>{duplicates}</span></div>'

    # Escape field name for HTML attribute
    field_name_escaped = html.escape(field_name)

    # Determine severity based on duplicate count
    if duplicates > 100:
        badge_class = "bg-danger"
        icon_class = "fas fa-exclamation-triangle"
        severity_text = "حرج"
    elif duplicates > 50:
        badge_class = "bg-warning"
        icon_class = "fas fa-exclamation-circle"
        severity_text = "متوسط"
    else:
        badge_class = "bg-info"
        icon_class = "fas fa-info-circle"
        severity_text = "منخفض"

    # Get comprehensive duplicate information if DataFrame is available
    detailed_duplicates_info = {}
    duplicate_preview_html = ""
    total_unique_duplicates = 0
    total_rows_with_duplicates = 0

    if df is not None and field_name in df.columns:
        try:
            # Find all rows with duplicate values in this field
            field_data = df[field_name]
            duplicate_values = field_data[field_data.duplicated(keep=False)]

            total_unique_duplicates = len(duplicate_values.unique())
            total_rows_with_duplicates = len(duplicate_values)

            # Create comprehensive preview table showing ALL duplicate data
            if len(duplicate_values.unique()) > 0:
                duplicate_preview_html = f'''
                <div class="mt-2">
                    <small class="text-muted fw-bold">معاينة شاملة للقيم المكررة ({total_unique_duplicates} قيمة فريدة، {total_rows_with_duplicates} صف كامل):</small>
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-sm table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th style="font-size: 11px;">القيمة المكررة</th>
                                    <th style="font-size: 11px;">عدد التكرار</th>
                                    <th style="font-size: 11px;">جميع البيانات للصفوف المكررة</th>
                                </tr>
                            </thead>
                            <tbody>
                '''

                # Show ALL duplicate values with their complete details
                for i, dup_value in enumerate(duplicate_values.unique()):
                    if pd.notna(dup_value):
                        # Get all rows with this duplicate value
                        duplicate_rows = df[field_data == dup_value]
                        count = len(duplicate_rows)

                        # Create comprehensive data preview (ALL columns for ALL rows) in Excel-like table format
                        all_rows_html = f'''
                        <table class="table table-sm table-striped table-bordered">
                            <thead class="table-light">
                                <tr>
                        '''

                        # Add column headers
                        for col in df.columns:
                            all_rows_html += f'<th style="font-size: 10px; font-weight: bold;">{col}</th>'
                        all_rows_html += '</tr></thead><tbody>'

                        # Add data rows
                        for idx, row in duplicate_rows.iterrows():
                            all_rows_html += '<tr>'
                            for col in df.columns:
                                cell_value = str(row[col])[:50] if pd.notna(row[col]) else 'فارغ'
                                all_rows_html += f'<td style="font-size: 9px;">{cell_value}</td>'
                            all_rows_html += '</tr>'

                        all_rows_html += '</tbody></table>'

                        duplicate_preview_html += f'''
                            <tr>
                                <td style="font-size: 11px;"><code>{str(dup_value)[:50]}</code></td>
                                <td style="font-size: 11px;"><span class="badge bg-warning">{count}</span></td>
                                <td style="font-size: 10px; max-width: 500px;">{all_rows_html}</td>
                            </tr>
                        '''

                        # Store complete detailed information for ALL rows
                        detailed_duplicates_info[str(dup_value)] = {
                            'count': count,
                            'rows_data': duplicate_rows.to_dict('records'),  # Include ALL rows for complete data review
                            'all_columns': list(df.columns),
                            'sample_row': duplicate_rows.iloc[0].to_dict() if len(duplicate_rows) > 0 else {},
                            'duplicate_value': str(dup_value),
                            'all_rows_complete': duplicate_rows.to_dict('index'),  # Complete data for all rows
                            'row_indices': list(duplicate_rows.index)  # Row indices for reference
                        }

                duplicate_preview_html += '''
                            </tbody>
                        </table>
                    </div>
                </div>
                '''

        except Exception as e:
            logger.warning(f"Error getting detailed duplicate info for {field_name}: {e}")
            duplicate_preview_html = f'<div class="text-warning small mt-1">خطأ في تحميل التفاصيل: {str(e)}</div>'

    # Create comprehensive formatted HTML with preview and export options
    formatted_html = f'''
    <div class="duplicate-details-container">
        <div class="d-flex align-items-center justify-content-center mb-2">
            <div class="text-center me-2">
                <span class="badge {badge_class} fs-6 px-2 py-1" title="{severity_text}">
                    <i class="{icon_class} me-1"></i>
                    {duplicates:,}
                </span>
                <br>
                <small class="text-muted">{severity_text}</small>
                {f'<br><small class="text-info">{total_unique_duplicates} قيمة فريدة</small>' if total_unique_duplicates > 0 else ''}
                {f'<br><small class="text-primary">{total_rows_with_duplicates} صف كامل</small>' if total_rows_with_duplicates > 0 else ''}
            </div>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle p-1 me-1" type="button"
                        data-bs-toggle="dropdown" aria-expanded="false"
                        title="خيارات التصدير والعرض التفصيلي"
                        style="border: none; background: transparent; user-select: auto !important; cursor: pointer !important;">
                    <i class="fas fa-download fa-sm text-primary"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><h6 class="dropdown-header">تصدير القيم المكررة</h6></li>
                    <li><a class="dropdown-item export-duplicates-btn" href="#" data-format="excel"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-file-excel me-2 text-success"></i>تصدير Excel (القيم فقط)
                    </a></li>
                    <li><a class="dropdown-item export-duplicates-btn" href="#" data-format="csv"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-file-csv me-2 text-info"></i>تصدير CSV (القيم فقط)
                    </a></li>
                    <li><a class="dropdown-item export-duplicates-btn" href="#" data-format="json"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-file-code me-2 text-warning"></i>تصدير JSON (القيم فقط)
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">تصدير البيانات الكاملة</h6></li>
                    <li><a class="dropdown-item export-full-duplicate-rows-btn" href="#" data-format="excel"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-table me-2 text-danger"></i>تصدير Excel (جميع الصفوف والأعمدة)
                    </a></li>
                    <li><a class="dropdown-item export-full-duplicate-rows-btn" href="#" data-format="csv"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-file-csv me-2 text-danger"></i>تصدير CSV (جميع الصفوف والأعمدة)
                    </a></li>
                    <li><a class="dropdown-item export-full-duplicate-rows-btn" href="#" data-format="json"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(duplicate_details, ensure_ascii=False))}">
                        <i class="fas fa-file-code me-2 text-danger"></i>تصدير JSON (جميع الصفوف والأعمدة)
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><h6 class="dropdown-header">العرض التفاعلي</h6></li>
                    <li><a class="dropdown-item show-duplicate-details-btn" href="#"
                            data-field="{field_name_escaped}"
                            data-details="{html.escape(json.dumps(detailed_duplicates_info, ensure_ascii=False))}"
                            data-toggle="modal" data-target="#duplicateDetailsModal">
                        <i class="fas fa-eye me-2 text-primary"></i>عرض تفاعلي مفصل (جميع البيانات)
                    </a></li>
                </ul>
            </div>
        </div>
        {duplicate_preview_html}
    </div>
    '''
    return formatted_html

def format_unique_details_for_table(field_name, unique_count, unique_details):
    """Format unique details for table display with dropdown menu for export options"""
    import html
    if unique_count == 0:
        return f'<div class="text-center"><span class="badge bg-warning fs-6 px-2 py-1"><i class="fas fa-exclamation-triangle me-1"></i>{unique_count}</span></div>'

    # Escape field name for HTML attribute
    field_name_escaped = html.escape(field_name)

    # Determine quality based on unique count
    if unique_count > 1000:
        badge_class = "bg-success"
        icon_class = "fas fa-star"
        quality_text = "ممتاز"
    elif unique_count > 100:
        badge_class = "bg-info"
        icon_class = "fas fa-check-circle"
        quality_text = "جيد"
    elif unique_count > 10:
        badge_class = "bg-warning"
        icon_class = "fas fa-info-circle"
        quality_text = "متوسط"
    else:
        badge_class = "bg-danger"
        icon_class = "fas fa-exclamation-triangle"
        quality_text = "ضعيف"

    # Create formatted HTML with dropdown menu for export options
    formatted_html = f'''
    <div class="d-flex align-items-center justify-content-center">
        <div class="text-center me-2">
            <span class="badge {badge_class} fs-6 px-2 py-1" title="{quality_text}">
                <i class="{icon_class} me-1"></i>
                {unique_count:,}
            </span>
            <br>
            <small class="text-muted">{quality_text}</small>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-success dropdown-toggle p-1" type="button"
                    data-bs-toggle="dropdown" aria-expanded="false"
                    title="خيارات تصدير القيم الفريدة"
                    style="border: none; background: transparent; user-select: auto !important; cursor: pointer !important;">
                <i class="fas fa-download fa-sm text-success"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="excel"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-excel me-2 text-success"></i>تصدير Excel
                </a></li>
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="csv"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-csv me-2 text-info"></i>تصدير CSV
                </a></li>
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="json"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-code me-2 text-warning"></i>تصدير JSON
                </a></li>
            </ul>
        </div>
    </div>
    '''
    return formatted_html

def format_unique_details_for_table(field_name, unique_count, unique_details):
    """Format unique details for table display with dropdown menu for export options"""
    import html
    if unique_count == 0:
        return f'<div class="text-center"><span class="badge bg-warning fs-6 px-2 py-1"><i class="fas fa-exclamation-triangle me-1"></i>{unique_count}</span></div>'

    # Escape field name for HTML attribute
    field_name_escaped = html.escape(field_name)

    # Determine quality based on unique count
    if unique_count > 1000:
        badge_class = "bg-success"
        icon_class = "fas fa-star"
        quality_text = "ممتاز"
    elif unique_count > 100:
        badge_class = "bg-info"
        icon_class = "fas fa-check-circle"
        quality_text = "جيد"
    elif unique_count > 10:
        badge_class = "bg-warning"
        icon_class = "fas fa-info-circle"
        quality_text = "متوسط"
    else:
        badge_class = "bg-danger"
        icon_class = "fas fa-exclamation-triangle"
        quality_text = "ضعيف"

    # Create formatted HTML with dropdown menu for export options
    formatted_html = f'''
    <div class="d-flex align-items-center justify-content-center">
        <div class="text-center me-2">
            <span class="badge {badge_class} fs-6 px-2 py-1" title="{quality_text}">
                <i class="{icon_class} me-1"></i>
                {unique_count:,}
            </span>
            <br>
            <small class="text-muted">{quality_text}</small>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-success dropdown-toggle p-1" type="button"
                    data-bs-toggle="dropdown" aria-expanded="false"
                    title="خيارات تصدير القيم الفريدة"
                    style="border: none; background: transparent; user-select: auto !important; cursor: pointer !important;">
                <i class="fas fa-download fa-sm text-success"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="excel"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-excel me-2 text-success"></i>تصدير Excel
                </a></li>
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="csv"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-csv me-2 text-info"></i>تصدير CSV
                </a></li>
                <li><a class="dropdown-item export-uniques-btn" href="#" data-format="json"
                       data-field="{field_name_escaped}"
                       data-details="{html.escape(json.dumps(unique_details, ensure_ascii=False))}">
                    <i class="fas fa-file-code me-2 text-warning"></i>تصدير JSON
                </a></li>
            </ul>
        </div>
    </div>
    '''
    return formatted_html

def get_quality_badge_class(notes):
    if 'ممتازة' in notes or 'عالية' in notes:
        return 'badge bg-success'
    elif 'متوسطة' in notes:
        return 'badge bg-warning'
    else:
        return 'badge bg-danger'

def get_status_badge_class(status_code):
    if status_code == 'OK':
        return 'badge bg-success'
    elif status_code == 'WARNING':
        return 'badge bg-warning'
    else:
        return 'badge bg-danger'

def get_priority_badge_class(priority):
    if priority == 'عالية':
        return 'badge bg-danger'
    elif priority == 'متوسطة':
        return 'badge bg-warning'
    else:
        return 'badge bg-success'

def export_comprehensive_pdf_report(results, language='ar', report_type='executive_summary',
                                   include_charts=True, include_metrics=True,
                                   include_recommendations=True, target_audience='decision_maker'):
    """Generate comprehensive executive report PDF for decision makers"""
    try:
        from io import BytesIO
        import arabic_reshaper
        from bidi.algorithm import get_display

        buffer = BytesIO()

        # Create PDF document in landscape orientation with larger page size
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib import colors

        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), encoding='utf-8')
        elements = []

        # Register Arabic font if available
        try:
            import os
            font_paths = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/tahoma.ttf',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/System/Library/Fonts/Arial.ttf',
            ]

            arabic_font_path = None
            for path in font_paths:
                if os.path.exists(path):
                    arabic_font_path = path
                    break

            if arabic_font_path:
                pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                arabic_font = 'Arabic'
            else:
                arabic_font = 'Helvetica'
        except Exception as e:
            logger.warning(f"Could not register Arabic font: {e}")
            arabic_font = 'Helvetica'

        # Styles
        styles = getSampleStyleSheet()

        # Create custom styles with Arabic font
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=20,
            alignment=1,  # Center
            fontName=arabic_font,
            encoding='utf-8'
        )

        arabic_style = ParagraphStyle(
            'ArabicStyle',
            parent=styles['Normal'],
            fontSize=10,
            fontName=arabic_font,
            encoding='utf-8',
            alignment=2  # Right alignment for Arabic
        )

        # Arabic text reshaping function
        def arabic_text(text):
            try:
                if isinstance(text, str) and any('\u0600' <= char <= '\u06FF' for char in text):
                    reshaped = arabic_reshaper.reshape(text)
                    return get_display(reshaped)
                return text
            except Exception as e:
                logger.warning(f"Arabic text processing error: {e}")
                return text

        # Language-specific content
        if language == 'en':
            title_text = "Executive Summary Report - Data Quality Analysis"
            subtitle_text = "Comprehensive Analysis for Decision Makers"
            executive_summary_title = "Executive Summary"
            metrics_title_text = "Data Quality Metrics"
            analysis_title_text = "Detailed Field Analysis"
            recommendations_title = "Recommendations and Action Items"
        else:
            title_text = arabic_text("تقرير ملخص تنفيذي - تحليل جودة البيانات")
            subtitle_text = arabic_text("تحليل شامل لمتخذي القرارات")
            executive_summary_title = arabic_text("الملخص التنفيذي")
            metrics_title_text = arabic_text("مؤشرات جودة البيانات")
            analysis_title_text = arabic_text("تحليل مفصل للحقول")
            recommendations_title = arabic_text("التوصيات والإجراءات المطلوبة")

        # Title Page
        title = Paragraph(title_text, title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        subtitle = Paragraph(subtitle_text, styles['Heading2'])
        elements.append(subtitle)
        elements.append(Spacer(1, 20))

        # Executive Summary
        exec_summary = Paragraph(executive_summary_title, styles['Heading2'])
        elements.append(exec_summary)
        elements.append(Spacer(1, 12))

        field_analysis = results.get('field_analysis', [])
        quality_metrics = results.get('quality_metrics', {})

        # Generate executive summary content
        total_fields = len(field_analysis)
        fields_with_issues = sum(1 for f in field_analysis if f.get('null_values', 0) > 0 or f.get('outlier_values', 0) > 0)
        critical_issues = sum(1 for f in field_analysis if f.get('null_values', 0) > 10 or f.get('outlier_values', 0) > 5)

        if language == 'en':
            summary_text = f"""
            This comprehensive report provides an in-depth analysis of {total_fields} data fields.
            Key findings include:
            • {fields_with_issues} fields have data quality issues
            • {critical_issues} fields require immediate attention
            • Overall data quality assessment completed
            """
        else:
            summary_text = arabic_text(f"""
            يقدم هذا التقرير الشامل تحليلاً عميقاً لـ {total_fields} حقل بيانات.
            أبرز النتائج تشمل:
            • {fields_with_issues} حقل يحتوي على مشاكل في جودة البيانات
            • {critical_issues} حقل يتطلب انتباهاً فورياً
            • تم إكمال تقييم شامل لجودة البيانات
            """)

        summary_para = Paragraph(summary_text, arabic_style)
        elements.append(summary_para)
        elements.append(Spacer(1, 20))

        # Quality Metrics Section
        if include_metrics:
            elements.append(PageBreak())
            metrics_title = Paragraph(metrics_title_text, styles['Heading2'])
            elements.append(metrics_title)
            elements.append(Spacer(1, 12))

            metrics_data = [
                [arabic_text('المؤشر'), arabic_text('القيمة'), arabic_text('النسبة المئوية')],
                [arabic_text('التكرار غير المنطقي'), str(quality_metrics.get('irregular_repetition', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('irregular_repetition', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('نواقص الإدخال'), str(quality_metrics.get('input_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('input_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('نواقص الكلمات'), str(quality_metrics.get('word_gaps', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('word_gaps', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('اختلالات الخانات'), str(quality_metrics.get('length_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('length_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('اختلالات التسلسل'), str(quality_metrics.get('sequence_irregularities', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('sequence_irregularities', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"],
                [arabic_text('النتائج الشاذة'), str(quality_metrics.get('outlier_values', 0)), f"{quality_metrics.get('total', 0) and (quality_metrics.get('outlier_values', 0) / quality_metrics.get('total', 1)) * 100:.1f}%"]
            ]

            metrics_table = Table(metrics_data)
            metrics_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), arabic_font),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 1), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
            ]))
            elements.append(metrics_table)
            elements.append(Spacer(1, 15))

        # Detailed Analysis Section (Top 10 critical fields)
        elements.append(PageBreak())
        analysis_title = Paragraph(analysis_title_text, styles['Heading2'])
        elements.append(analysis_title)
        elements.append(Spacer(1, 12))

        # Sort fields by criticality (nulls + outliers)
        critical_fields = sorted(field_analysis,
                               key=lambda x: x.get('null_values', 0) + x.get('outlier_values', 0),
                               reverse=True)[:10]  # Top 10

        # Create summary table for critical fields
        table_data = [[arabic_text('اسم الحقل'), arabic_text('القيم الناقصة'), arabic_text('القيم الشاذة'), arabic_text('الأولوية')]]

        for field in critical_fields:
            priority = 'عالية' if (field.get('null_values', 0) + field.get('outlier_values', 0)) > 10 else 'متوسطة'
            table_data.append([
                arabic_text(field.get('field_name', '')),
                str(field.get('null_values', 0)),
                str(field.get('outlier_values', 0)),
                arabic_text(priority)
            ])

        if len(table_data) > 1:
            analysis_table = Table(table_data)
            analysis_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), arabic_font),
                ('FONTSIZE', (0, 0), (-1, 0), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 1), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 1), (-1, -1), 6),
            ]))
            elements.append(analysis_table)

        # Recommendations Section
        if include_recommendations:
            elements.append(PageBreak())
            rec_title = Paragraph(recommendations_title, styles['Heading2'])
            elements.append(rec_title)
            elements.append(Spacer(1, 12))

            recommendations = [
                arabic_text("1. إعطاء الأولوية لحقول البيانات التي تحتوي على نسبة عالية من القيم الناقصة"),
                arabic_text("2. مراجعة وتصحيح القيم الشاذة المكتشفة في التحليل"),
                arabic_text("3. تحسين عمليات التحقق من صحة البيانات عند الإدخال"),
                arabic_text("4. تدريب المستخدمين على أهمية جودة البيانات"),
                arabic_text("5. إنشاء آليات للتحقق المستمر من جودة البيانات")
            ]

            for rec in recommendations:
                rec_para = Paragraph(rec, arabic_style)
                elements.append(rec_para)
                elements.append(Spacer(1, 6))

        # Build PDF
        doc.build(elements)

        buffer.seek(0)
        pdf_data = buffer.getvalue()
        buffer.close()

        # Return PDF as response
        pdf_buffer = BytesIO(pdf_data)
        pdf_buffer.seek(0)

        return send_file(
            pdf_buffer,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'comprehensive_executive_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
        )

    except Exception as e:
        logger.error(f"Comprehensive PDF report error: {str(e)}")
        return jsonify({'error': f'Comprehensive report generation failed: {str(e)}'}), 500

def export_full_duplicate_rows(field_name, detailed_duplicates_info, export_format='excel'):
    """Export complete duplicate rows data with all columns and details"""
    try:
        if not detailed_duplicates_info:
            return jsonify({'error': 'لا توجد بيانات مكررة للتصدير'}), 400

        # Prepare comprehensive data for export
        export_data = {
            'field_name': field_name,
            'export_timestamp': datetime.now().isoformat(),
            'total_duplicate_values': len(detailed_duplicates_info),
            'duplicate_details': {}
        }

        all_rows_data = []

        # Collect all duplicate rows with complete information
        for dup_value, dup_info in detailed_duplicates_info.items():
            export_data['duplicate_details'][dup_value] = {
                'duplicate_value': dup_value,
                'count': dup_info['count'],
                'all_columns': dup_info['all_columns'],
                'sample_row': dup_info['sample_row']
            }

            # Add all rows data for complete export
            for row_data in dup_info['rows_data']:
                row_data_copy = row_data.copy()
                row_data_copy['_duplicate_value'] = dup_value
                row_data_copy['_duplicate_count'] = dup_info['count']
                all_rows_data.append(row_data_copy)

        export_data['all_duplicate_rows'] = all_rows_data
        export_data['total_rows_exported'] = len(all_rows_data)

        if export_format == 'excel':
            return export_full_duplicate_rows_excel(field_name, export_data)
        elif export_format == 'csv':
            return export_full_duplicate_rows_csv(field_name, export_data)
        elif export_format == 'json':
            return export_full_duplicate_rows_json(field_name, export_data)
        else:
            return jsonify({'error': f'تنسيق التصدير غير مدعوم: {export_format}'}), 400

    except Exception as e:
        logger.error(f"Full duplicate rows export error: {str(e)}")
        return jsonify({'error': f'فشل في تصدير الصفوف المكررة الكاملة: {str(e)}'}), 500

def export_full_duplicate_rows_excel(field_name, export_data):
    """Export complete duplicate rows to Excel with multiple sheets"""
    try:
        from io import BytesIO
        import xlsxwriter

        buffer = BytesIO()
        workbook = xlsxwriter.Workbook(buffer)

        # Summary sheet
        summary_sheet = workbook.add_worksheet('ملخص المكررات')
        summary_format = workbook.add_format({
            'bold': True,
            'bg_color': '#E6F3FF',
            'border': 1,
            'align': 'center'
        })
        data_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'text_wrap': True
        })

        # Write summary header
        summary_sheet.write(0, 0, 'معلومات التصدير', summary_format)
        summary_sheet.write(0, 1, 'القيمة', data_format)

        summary_data = [
            ('اسم الحقل', export_data['field_name']),
            ('تاريخ التصدير', export_data['export_timestamp']),
            ('عدد القيم المكررة الفريدة', export_data['total_duplicate_values']),
            ('إجمالي الصفوف المصدرة', export_data['total_rows_exported'])
        ]

        for i, (label, value) in enumerate(summary_data, 1):
            summary_sheet.write(i, 0, label, summary_format)
            summary_sheet.write(i, 1, str(value), data_format)

        # Duplicate details sheet
        details_sheet = workbook.add_worksheet('تفاصيل المكررات')
        details_sheet.write(0, 0, 'القيمة المكررة', summary_format)
        details_sheet.write(0, 1, 'عدد التكرار', summary_format)
        details_sheet.write(0, 2, 'الأعمدة المتاحة', summary_format)

        for i, (dup_value, dup_info) in enumerate(export_data['duplicate_details'].items(), 1):
            details_sheet.write(i, 0, dup_value, data_format)
            details_sheet.write(i, 1, dup_info['count'], data_format)
            details_sheet.write(i, 2, ', '.join(dup_info['all_columns']), data_format)

        # Complete data sheet - Export ALL duplicate rows with ALL columns
        data_sheet = workbook.add_worksheet('البيانات الكاملة')

        # Get all columns from the first row
        if export_data['all_duplicate_rows']:
            all_columns = list(export_data['all_duplicate_rows'][0].keys())

            # Write headers
            for col_idx, col_name in enumerate(all_columns):
                data_sheet.write(0, col_idx, col_name, summary_format)

            # Write ALL data rows with ALL columns
            for row_idx, row_data in enumerate(export_data['all_duplicate_rows'], 1):
                for col_idx, col_name in enumerate(all_columns):
                    value = row_data.get(col_name, '')
                    # Handle different data types properly
                    if pd.isna(value):
                        display_value = ''
                    else:
                        display_value = str(value)
                    data_sheet.write(row_idx, col_idx, display_value, data_format)

        # Create separate sheets for each duplicate value with complete data
        for dup_value, dup_info in export_data['duplicate_details'].items():
            # Create safe sheet name
            safe_sheet_name = str(dup_value)[:30].replace('/', '_').replace('\\', '_').replace('?', '_').replace('*', '_').replace('[', '_').replace(']', '_')
            if len(safe_sheet_name.strip()) == 0:
                safe_sheet_name = f"duplicate_{list(export_data['duplicate_details'].keys()).index(dup_value) + 1}"

            try:
                dup_sheet = workbook.add_worksheet(f'قيمة_{safe_sheet_name}')
            except:
                # Fallback if sheet name is invalid
                dup_sheet = workbook.add_worksheet(f'dup_{list(export_data['duplicate_details'].keys()).index(dup_value) + 1}')

            # Write headers for this duplicate value
            dup_sheet.write(0, 0, 'معلومات القيمة المكررة', summary_format)
            dup_sheet.write(0, 1, 'التفاصيل', data_format)
            dup_sheet.write(1, 0, 'القيمة المكررة', summary_format)
            dup_sheet.write(1, 1, dup_value, data_format)
            dup_sheet.write(2, 0, 'عدد التكرار', summary_format)
            dup_sheet.write(2, 1, dup_info['count'], data_format)
            dup_sheet.write(3, 0, 'الأعمدة المتاحة', summary_format)
            dup_sheet.write(3, 1, ', '.join(dup_info['all_columns']), data_format)

            # Write ALL rows for this duplicate value with ALL columns
            if dup_info['rows_data']:
                # Write column headers starting from row 5
                for col_idx, col_name in enumerate(dup_info['all_columns']):
                    dup_sheet.write(5, col_idx, col_name, summary_format)

                # Write ALL data rows for this duplicate value
                for row_idx, row_data in enumerate(dup_info['rows_data'], 6):
                    for col_idx, col_name in enumerate(dup_info['all_columns']):
                        value = row_data.get(col_name, '')
                        if pd.isna(value):
                            display_value = ''
                        else:
                            display_value = str(value)
                        dup_sheet.write(row_idx, col_idx, display_value, data_format)

        workbook.close()
        buffer.seek(0)

        return send_file(
            buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'full_duplicate_rows_complete_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )

    except Exception as e:
        logger.error(f"Excel full duplicate export error: {str(e)}")
        return jsonify({'error': f'فشل في تصدير Excel: {str(e)}'}), 500

def export_full_duplicate_rows_csv(field_name, export_data):
    """Export complete duplicate rows to CSV"""
    try:
        import csv
        from io import StringIO

        output = StringIO()

        # Write metadata
        writer = csv.writer(output)
        writer.writerow(['معلومات التصدير'])
        writer.writerow(['اسم الحقل', export_data['field_name']])
        writer.writerow(['تاريخ التصدير', export_data['export_timestamp']])
        writer.writerow(['عدد القيم المكررة الفريدة', export_data['total_duplicate_values']])
        writer.writerow(['إجمالي الصفوف المصدرة', export_data['total_rows_exported']])
        writer.writerow([])  # Empty row

        # Write duplicate details summary
        writer.writerow(['تفاصيل المكررات'])
        writer.writerow(['القيمة المكررة', 'عدد التكرار', 'الأعمدة المتاحة'])
        for dup_value, dup_info in export_data['duplicate_details'].items():
            writer.writerow([
                dup_value,
                dup_info['count'],
                ', '.join(dup_info['all_columns'])
            ])
        writer.writerow([])  # Empty row

        # Write complete data
        writer.writerow(['البيانات الكاملة للمكررات'])
        if export_data['all_duplicate_rows']:
            headers = list(export_data['all_duplicate_rows'][0].keys())
            writer.writerow(headers)

            for row_data in export_data['all_duplicate_rows']:
                row = [str(row_data.get(col, '')) for col in headers]
                writer.writerow(row)

        csv_data = output.getvalue()
        output.close()

        # Return CSV with UTF-8 BOM for Excel compatibility
        from flask import Response
        csv_data_with_bom = '\ufeff' + csv_data
        return Response(
            csv_data_with_bom,
            mimetype='text/csv; charset=utf-8',
            headers={
                'Content-Disposition': f'attachment; filename=full_duplicate_rows_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )

    except Exception as e:
        logger.error(f"CSV full duplicate export error: {str(e)}")
        return jsonify({'error': f'فشل في تصدير CSV: {str(e)}'}), 500

def export_full_duplicate_rows_json(field_name, export_data):
    """Export complete duplicate rows to JSON"""
    try:
        from flask import Response

        # Add export metadata
        export_data['export_format'] = 'json'
        export_data['export_type'] = 'full_duplicate_rows'

        return Response(
            json.dumps(export_data, ensure_ascii=False, indent=2),
            mimetype='application/json; charset=utf-8',
            headers={'Content-Disposition': f'attachment; filename=full_duplicate_rows_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'}
        )

    except Exception as e:
        logger.error(f"JSON full duplicate export error: {str(e)}")
        return jsonify({'error': f'فشل في تصدير JSON: {str(e)}'}), 500

def export_json(field_analysis, quality_metrics):
    """Export results as JSON"""
    try:
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'title': 'تقرير المراجعة المستقلة - تحليل الحقول النصية',
            'quality_metrics': quality_metrics,
            'field_analysis': field_analysis,
            'summary': {
                'total_fields': len(field_analysis),
                'fields_with_issues': sum(1 for f in field_analysis if f.get('null_values', 0) > 0 or f.get('outlier_values', 0) > 0),
                'export_format': 'json'
            }
        }

        from flask import Response
        return Response(
            json.dumps(export_data, ensure_ascii=False, indent=2),
            mimetype='application/json; charset=utf-8',
            headers={'Content-Disposition': f'attachment; filename=independent_review_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'}
        )

    except Exception as e:
        logger.error(f"JSON export error: {str(e)}")
        return jsonify({'error': f'JSON export failed: {str(e)}'}), 500

def create_database_tables():
    """إنشاء جداول قاعدة البيانات"""
    try:
        # الحصول على مدير قاعدة البيانات
        db_manager = get_db_manager()

        # إنشاء الجداول الأساسية
        with db_manager.get_connection() as conn:
            if conn is None:
                logger.warning("[DB_MANAGER] Database connection is None - skipping table creation")
                return False

            cursor = conn.cursor()

            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    role TEXT NOT NULL DEFAULT 'viewer',
                    sector_access TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT NOT NULL,
                    last_login TEXT,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until TEXT
                )
            ''')

            # جدول جلسات المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    login_time TEXT NOT NULL,
                    logout_time TEXT,
                    is_active INTEGER DEFAULT 1,
                    session_expiry TEXT,
                    remember_me INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # جدول سجل الأنشطة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS activity_log (
                    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource_type TEXT,
                    resource_id TEXT,
                    details TEXT,
                    ip_address TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')

            # جدول المعايير المخصصة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS custom_criteria (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    criteria_type TEXT NOT NULL,
                    criteria_config TEXT NOT NULL,
                    sector TEXT DEFAULT 'all',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    created_by TEXT
                )
            ''')

            # جدول إعدادات النظام
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    setting_key TEXT PRIMARY KEY,
                    setting_value TEXT NOT NULL,
                    setting_type TEXT DEFAULT 'string',
                    updated_at TEXT NOT NULL,
                    updated_by TEXT
                )
            ''')

            # إنشاء الفهارس
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_active ON user_sessions(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_sessions_expiry ON user_sessions(session_expiry)",
                "CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_log(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_activity_action ON activity_log(action)",
                "CREATE INDEX IF NOT EXISTS idx_activity_timestamp ON activity_log(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_criteria_sector ON custom_criteria(sector)",
                "CREATE INDEX IF NOT EXISTS idx_criteria_active ON custom_criteria(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_settings_key ON system_settings(setting_key)"
            ]

            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                except Exception as e:
                    logger.warning(f"[DB_MANAGER] خطأ في إنشاء الفهرس: {e}")

            conn.commit()
            logger.info("[DB_MANAGER] تم إنشاء جداول قاعدة البيانات بنجاح")
            return True

    except Exception as e:
        logger.error(f"[DB_MANAGER] خطأ في إنشاء جداول قاعدة البيانات: {e}")
        return False

if __name__ == '__main__':
    print("Starting server with detailed logging...")
    import logging
    logging.basicConfig(level=logging.INFO, encoding='utf-8')

    # Try different ports if 2000 is busy
    ports_to_try = [2000, 5000, 8000, 3000, 4000]

    for port in ports_to_try:
        try:
            print(f"Trying to start server on port {port}...")
            app.run(host='0.0.0.0', port=port, debug=False, threaded=True, use_reloader=False)
            break
        except OSError as e:
            print(f"Port {port} is busy, trying next port...")
            continue
    else:
        print("All ports are busy. Please check running processes.")
