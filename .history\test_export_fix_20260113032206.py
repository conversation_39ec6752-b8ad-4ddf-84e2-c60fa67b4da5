#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that the export functionality is working correctly
"""

import requests
import json
import sys

def test_export_endpoints():
    """Test the export endpoints that were fixed"""
    base_url = "http://127.0.0.1:2000"
    
    # Test data for indicator details export
    indicator_test_data = {
        "indicator_details": {
            "summary": {
                "indicator": "Test Indicator",
                "total_input_gaps": 10,
                "affected_fields": 3,
                "export_timestamp": "2024-11-04 12:00:00"
            },
            "field_details": [
                {
                    "field_name": "Test Field 1",
                    "null_values": 5,
                    "outlier_values": 2,
                    "duplicate_values": 3,
                    "percentage": "50%",
                    "risk_level": "متوسط"
                },
                {
                    "field_name": "Test Field 2", 
                    "null_values": 3,
                    "outlier_values": 1,
                    "duplicate_values": 2,
                    "percentage": "30%",
                    "risk_level": "منخفض"
                }
            ]
        }
    }
    
    # Test irregular repetitions export
    irregular_test_data = {
        "irregular_details": {
            "Value 1": 15,
            "Value 2": 10,
            "Value 3": 8,
            "Value 4": 5
        }
    }
    
    # Test unique values export
    unique_test_data = {
        "field_name": "test_field"
    }
    
    endpoints_to_test = [
        {
            "url": f"{base_url}/api/independent-review/export/indicator-details/",
            "data": indicator_test_data,
            "name": "Indicator Details Export"
        },
        {
            "url": f"{base_url}/api/independent-review/export/irregular-repetitions/",
            "data": irregular_test_data,
            "name": "Irregular Repetitions Export"
        },
        {
            "url": f"{base_url}/api/independent-review/export/unique-values/excel",
            "data": unique_test_data,
            "name": "Unique Values Excel Export"
        }
    ]
    
    print("🧪 Testing Export Endpoints...")
    print("=" * 50)
    
    session = requests.Session()
    
    for endpoint in endpoints_to_test:
        try:
            print(f"\nTesting: {endpoint['name']}")
            print(f"URL: {endpoint['url']}")

            response = session.post(
                endpoint['url'],
                json=endpoint['data'],
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                # Check if it's an Excel file
                content_type = response.headers.get('content-type', '')
                if 'spreadsheet' in content_type or 'excel' in content_type:
                    print("SUCCESS: Excel file returned")
                    print(f"Content-Type: {content_type}")
                    print(f"Content-Length: {len(response.content)} bytes")
                else:
                    print("WARNING: Not an Excel file")
                    print(f"Content-Type: {content_type}")
                    print(f"Response: {response.text[:200]}...")
            elif response.status_code == 401:
                print("AUTHENTICATION REQUIRED: This is expected for protected endpoints")
            elif response.status_code == 404:
                print("ERROR: Endpoint not found (404)")
            else:
                print(f"ERROR: Status {response.status_code}")
                print(f"Response: {response.text[:200]}...")

        except requests.exceptions.RequestException as e:
            print(f"CONNECTION ERROR: {str(e)}")
        except Exception as e:
            print(f"UNEXPECTED ERROR: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Export endpoint testing completed!")

if __name__ == "__main__":
    test_export_endpoints()
