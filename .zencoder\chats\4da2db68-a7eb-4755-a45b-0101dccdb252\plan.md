# Fix bug

## Workflow Steps

### [ ] Step: Investigation and Planning

Analyze the bug report and design a solution.

1. Review the bug description, error messages, and logs
2. Clarify reproduction steps with the user if unclear
3. Check existing tests for clues about expected behavior
4. Locate relevant code sections and identify root cause
5. Propose a fix based on the investigation
6. Consider edge cases and potential side effects

Save findings to `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\4da2db68-a7eb-4755-a45b-0101dccdb252/investigation.md` with:

- Bug summary
- Root cause analysis
- Affected components
- Proposed solution

### [ ] Step: Implementation

Read `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\4da2db68-a7eb-4755-a45b-0101dccdb252/investigation.md`
Implement the bug fix.

1. Add/adjust regression test(s) that fail before the fix and pass after
2. Implement the fix
3. Run relevant tests
4. Update `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\4da2db68-a7eb-4755-a45b-0101dccdb252/investigation.md` with implementation notes and test results

If blocked or uncertain, ask the user for direction.
