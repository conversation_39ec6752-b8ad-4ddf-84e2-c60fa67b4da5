# Full SDD workflow

## Workflow Steps

### [ ] Step: Requirements

Create a Product Requirements Document (PRD) based on the feature description.

1. Review existing codebase to understand current architecture and patterns
2. Analyze the feature definition and identify unclear aspects
3. Ask the user for clarifications on aspects that significantly impact scope or user experience
4. Make reasonable decisions for minor details based on context and conventions
5. If user can't clarify, make a decision, state the assumption, and continue

Save the PRD to `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/requirements.md`.

### [ ] Step: Technical Specification

Create a technical specification based on the PRD in `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/requirements.md`.

1. Review existing codebase architecture and identify reusable components
2. Define the implementation approach

Save to `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/spec.md` with:

- Technical context (language, dependencies)
- Implementation approach referencing existing code patterns
- Source code structure changes
- Data model / API / interface changes
- Delivery phases (incremental, testable milestones)
- Verification approach using project lint/test commands

### [ ] Step: Planning

Create a detailed implementation plan based on `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/spec.md`.

1. Break down the work into concrete tasks
2. Each task should reference relevant contracts and include verification steps
3. Replace the Implementation step below with the planned tasks

Rule of thumb for step size: each step should represent a coherent unit of work (e.g., implement a component, add an API endpoint, write tests for a module). Avoid steps that are too granular (single function) or too broad (entire feature).

If the feature is trivial and doesn't warrant full specification, update this workflow to remove unnecessary steps and explain the reasoning to the user.

Save to `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/plan.md`.

### [ ] Step: Implementation

This step should be replaced with detailed implementation tasks from the Planning step.

If Planning didn't replace this step, execute the tasks in `m:\نسخة مطورة لتحليل  قطاع التاميني\التامينات\.zencoder\chats\82447079-a367-452e-ba4e-4817117cbca4/plan.md`, updating checkboxes as you go. Run planned tests/lint and record results in plan.md.
