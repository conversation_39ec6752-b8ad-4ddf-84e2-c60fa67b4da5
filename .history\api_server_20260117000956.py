#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم API خارجي لنظام تحليل البيانات متعدد القطاعات
External API Server for Multi-Sector Data Analysis System
"""

import os
import json
import logging
import threading
import time
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Callable
from functools import wraps

# Import Flask and related libraries
from flask import Flask, request, jsonify, Blueprint, current_app, g, send_file
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
FLASK_AVAILABLE = True

from io import BytesIO, StringIO
import jwt as pyjwt
JWT_AVAILABLE = True

import uuid
import re

# Import pandas and other required libraries
import pandas as pd
PANDAS_AVAILABLE = True

# Import export libraries
import xlsxwriter
XLSXWRITER_AVAILABLE = True

# Arabic text processing functions
def fix_arabic_text(text):
    """Enhanced function to fix Arabic text that may be inverted or improperly encoded"""
    if not isinstance(text, str):
        return text

    # Remove any BOM characters that might cause issues
    text = text.replace('\ufeff', '').replace('\u200e', '').replace('\u200f', '')

    # Check if text contains Arabic characters
    has_arabic = bool(re.search(r'[\u0600-\u06FF]', text))

    if has_arabic:
        # For Arabic text, ensure proper RTL display
        # Remove extra spaces and normalize
        text = re.sub(r'\s+', ' ', text.strip())

        # Comprehensive dictionary of known reversed patterns
        known_reversed_patterns = {
            # الكلمات المذكورة في المشكلة الأصلية
            'عونلا': 'النوع',
            'عرفلا': 'الفرع',

            # الكلمات الجديدة المذكورة في المشكلة
            'ةلودلا': 'الدولة',
            'ةظفاحملا': 'المحافظة',
            'ةيسنجلا': 'الجنسية',
            'ةيناثلا هجوزلا مسا': 'اسم الزوجة الثانية',
            'هجوزلا مسا': 'اسم الزوجة',
            'ةيناثلا': 'الثانية',

            # كلمات شائعة أخرى
            'ناونعلا': 'العنوان',
            'مسالا': 'الاسم',
            'خيراتلا': 'التاريخ',
            'ةنيدملا': 'المدينة',
            'فتاهلا': 'الهاتف',
            'ديربلا': 'البريد',
            'سنجلا': 'الجنس',
            'رمعلا': 'العمر',
            'ةفيظولا': 'الوظيفة',
            'بتارلا': 'الراتب',
            'مسقلا': 'القسم',
            'ةكرشلا': 'الشركة',
            'ةسسؤملا': 'المؤسسة',
            'ةقطنملا': 'المنطقة',
            'دلبلا': 'البلد',
            'ةمدخلا': 'الخدمة',
            'جتنملا': 'المنتج',
            'ةعلسلا': 'السلعة',
            'رعسلا': 'السعر',
            'ةميقلا': 'القيمة',
            'ددعلا': 'العدد',
            'ةيمكلا': 'الكمية',
            'نزولا': 'الوزن',
            'لوطلا': 'الطول',
            'ضرعلا': 'العرض',
            'عافترالا': 'الارتفاع',
            'مجحلا': 'الحجم',
            'ةحاسملا': 'المساحة',
            'ةفاسملا': 'المسافة',
            'تقولا': 'الوقت',
            'ةعاسلا': 'الساعة',
            'ةقيقدلا': 'الدقيقة',
            'مويلا': 'اليوم',
            'رهشلا': 'الشهر',
            'ةنسلا': 'السنة',
            'عوبسالا': 'الأسبوع',

            # كلمات إضافية للزواج والعائلة
            'جوزلا': 'الزوج',
            'ةجوزلا': 'الزوجة',
            'لوالا جوزلا': 'الزوج الأول',
            'ىلوالا ةجوزلا': 'الزوجة الأولى',
            'ثلاثلا جوزلا': 'الزوج الثالث',
            'ةثلاثلا ةجوزلا': 'الزوجة الثالثة',
            'عبارلا جوزلا': 'الزوج الرابع',
            'ةعبارلا ةجوزلا': 'الزوجة الرابعة'
        }

        # Check for exact matches first - ONLY fix known reversed patterns
        if text in known_reversed_patterns:
            logger.info(f"[ARABIC_FIX] Found exact reversed pattern: {text} -> {known_reversed_patterns[text]}")
            return known_reversed_patterns[text]

        # Check if text is already in correct form (appears in the values of known patterns)
        correct_patterns = set(known_reversed_patterns.values())
        if text in correct_patterns:
            logger.info(f"[ARABIC_FIX] Text is already correct: {text}")
            return text

        # Check for partial matches within the text
        for reversed_pattern, correct_pattern in known_reversed_patterns.items():
            if reversed_pattern in text:
                logger.info(f"[ARABIC_FIX] Found reversed pattern within text: {reversed_pattern} in {text}")
                text = text.replace(reversed_pattern, correct_pattern)
                return text

        # If no known patterns found, return text unchanged
        logger.info(f"[ARABIC_FIX] No known reversed patterns found, keeping text unchanged: {text}")
        return text

    return text

def process_dataframe_arabic_text(df):
    """Process all text columns in a DataFrame to fix Arabic text issues"""
    df_processed = df.copy()

    for col in df_processed.columns:
        if df_processed[col].dtype == 'object':  # Text columns
            df_processed[col] = df_processed[col].astype(str).apply(fix_arabic_text)

    return df_processed

def format_duplicate_details_for_table(field_name, duplicates, duplicate_details):
    """Format duplicate details for table display with better HTML formatting"""
    import html
    import base64

    if duplicates == 0:
        return f'<div class="text-center"><span class="badge bg-success fs-6 px-2 py-1"><i class="fas fa-check-circle me-1"></i>{duplicates}</span></div>'

    # Escape field name for HTML attribute
    field_name_escaped = html.escape(field_name)

    # Determine severity based on duplicate count
    if duplicates > 100:
        badge_class = "bg-danger"
        icon_class = "fas fa-exclamation-triangle"
        severity_text = "حرج"
    elif duplicates > 50:
        badge_class = "bg-warning"
        icon_class = "fas fa-exclamation-circle"
        severity_text = "متوسط"
    else:
        badge_class = "bg-info"
        icon_class = "fas fa-info-circle"
        severity_text = "منخفض"

    # Encode duplicate details as base64 to avoid JSON parsing issues in HTML attributes
    try:
        json_str = json.dumps(duplicate_details, ensure_ascii=False)
        encoded_details = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
    except Exception as e:
        logger.warning(f"[FORMAT_DUPLICATES] Error encoding duplicate details: {e}")
        encoded_details = ""

    # Create formatted HTML with better styling
    formatted_html = f'''
    <div class="d-flex align-items-center justify-content-center">
        <div class="text-center me-2">
            <span class="badge {badge_class} fs-6 px-2 py-1" title="{severity_text}">
                <i class="{icon_class} me-1"></i>
                {duplicates:,}
            </span>
            <br>
            <small class="text-muted">{severity_text}</small>
        </div>
        <button class="btn btn-sm btn-outline-primary p-1 export-duplicates-btn"
                data-field="{field_name_escaped}"
                data-details="{encoded_details}"
                title="تصدير تفاصيل القيم المتكررة"
                style="border: none; background: transparent; user-select: auto !important; cursor: pointer !important;">
            <i class="fas fa-download fa-sm text-primary"></i>
        </button>
    </div>
    '''
    return formatted_html

# استيراد الأنظمة المحسنة
from database_manager import get_db_manager
from cache_manager import get_cache_manager, cached
from memory_monitor import get_performance_optimizer
from streaming_processor import StreamingDataProcessor
from sector_manager import SectorManager

logger = logging.getLogger(__name__)

class APIServer:
    """خادم API خارجي"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app = None
        self.api_blueprint = None
        self.sector_manager = SectorManager()
        self.streaming_processor = StreamingDataProcessor(config.get('streaming', {}))

        # إعدادات API
        self.jwt_secret = config.get('jwt_secret', 'your-secret-key-change-in-production')
        self.jwt_algorithm = config.get('jwt_algorithm', 'HS256')
        self.token_expiry = config.get('token_expiry_hours', 24)

        # إعدادات الأمان
        self.rate_limits = config.get('rate_limiting', {})
        self.allowed_origins = config.get('cors_origins', ['*'])

        # إعدادات الخادم
        self.host = config.get('host', '0.0.0.0')
        self.port = config.get('port', 2000)
        self.debug = config.get('debug', False)

        # متغيرات التشغيل
        self.server_thread = None
        self.running = False

        logger.info(f"[API_SERVER] تم تهيئة خادم API على {self.host}:{self.port}")

    def create_app(self) -> Flask:
        """إنشاء تطبيق Flask للـ API"""
        app = Flask(__name__)

        # إعداد CORS
        CORS(app, origins=self.allowed_origins)

        # إعدادات التطبيق
        app.config['SECRET_KEY'] = self.jwt_secret
        app.config['JSON_AS_ASCII'] = False

        # تسجيل Blueprint الـ API
        api_blueprint = self.create_api_blueprint()
        if api_blueprint is None:
            logger.error("[API] Failed to create API blueprint due to missing dependencies")
            raise ImportError("Required modules not available for API blueprint creation")
        app.register_blueprint(api_blueprint, url_prefix='/api')

        # إضافة middleware للأمان والمراقبة
        app.before_request(self.before_request)
        app.after_request(self.after_request)

        # معالجة الأخطاء
        app.register_error_handler(400, self.handle_bad_request)
        app.register_error_handler(401, self.handle_unauthorized)
        app.register_error_handler(403, self.handle_forbidden)
        app.register_error_handler(404, self.handle_not_found)
        app.register_error_handler(500, self.handle_internal_error)

        # إضافة route لخدمة ملف Chart.js
        @app.route('/chart.js')
        def serve_chart_js():
            """خدمة ملف Chart.js"""
            try:
                chart_js_path = os.path.join(os.getcwd(), 'static', 'chart.js')
                if os.path.exists(chart_js_path):
                    logger.info(f"[CHART_JS] Serving chart.js from: {chart_js_path}")
                    return send_file(chart_js_path, mimetype='application/javascript')
                else:
                    logger.error(f"[CHART_JS] Chart.js file not found at: {chart_js_path}")
                    return jsonify({'error': 'Chart.js file not found'}), 404
            except Exception as e:
                logger.error(f"[CHART_JS] Error serving chart.js: {e}")
                return jsonify({'error': 'Error serving Chart.js'}), 500

        self.app = app
        return app

    def create_api_blueprint(self) -> Blueprint:
        """إنشاء Blueprint للـ API"""
        api = Blueprint('api', __name__)

        # Import logger here to avoid circular import issues
        logger = logging.getLogger(__name__)

        # Check if required modules are available
        try:
            import flask
            import flask_cors
            import werkzeug
            import jwt as pyjwt
            import pandas
            import xlsxwriter
            import uuid
            import re
            import html
            import base64
            import io
            import threading
            import time
            import sys
            from datetime import datetime, timedelta
            logger.info("[API] All required modules are available")
        except ImportError as e:
            logger.error(f"[API] Missing required module: {e}")
            return None

        # Create the API blueprint
        api = Blueprint('api', __name__)

        # Routes الأساسية
        @api.route('/test', methods=['GET'])
        def test_endpoint():
            """نقطة اختبار للتحقق من الاتصال"""
            return jsonify({
                'status': 'success',
                'message': 'API server is running correctly',
                'timestamp': datetime.now().isoformat(),
                'server_info': {
                    'host': self.host,
                    'port': self.port,
                    'debug': self.debug
                }
            })

        @api.route('/status', methods=['GET'])
        def server_status():
            """حالة الخادم المفصلة"""
            return jsonify({
                'server_running': True,
                'uptime': 'active',
                'endpoints_available': [
                    '/api/v1/health',
                    '/api/v1/test',
                    '/api/v1/status',
                    '/api/v1/analyze',
                    '/api/v1/sectors',
                    '/api/v1/statistics'
                ],
                'features': [
                    'data_analysis',
                    'predictive_analytics',
                    'export_capabilities',
                    'backup_system',
                    'real_time_monitoring'
                ],
                'timestamp': datetime.now().isoformat()
            })

        @api.route('/auth/login', methods=['POST'])
        def login():
            """تسجيل الدخول وإرجاع token"""
            try:
                # Validate JSON input
                try:
                    data = request.get_json()
                    if not isinstance(data, dict):
                        return jsonify({'error': 'بيانات الطلب يجب أن تكون كائن JSON صحيح'}), 400
                except Exception as e:
                    logger.error(f"[API] خطأ في تحليل JSON: {e}")
                    return jsonify({'error': 'بيانات JSON غير صحيحة'}), 400

                username = data.get('username')
                password = data.get('password')

                # Validate required fields
                if not username or not password:
                    return jsonify({'error': 'اسم المستخدم وكلمة المرور مطلوبان'}), 400

                # Validate field types
                if not isinstance(username, str) or not isinstance(password, str):
                    return jsonify({'error': 'اسم المستخدم وكلمة المرور يجب أن يكونا نصوص'}), 400

                # Validate field lengths
                if len(username.strip()) == 0 or len(password.strip()) == 0:
                    return jsonify({'error': 'اسم المستخدم وكلمة المرور لا يمكن أن يكونا فارغين'}), 400

                # التحقق من بيانات المستخدم (يمكن ربطها بقاعدة البيانات)
                if self.authenticate_user(username, password):
                    token = self.generate_token(username)
                    return jsonify({
                        'token': token,
                        'expires_in': self.token_expiry * 3600,
                        'token_type': 'Bearer'
                    })
                else:
                    return jsonify({'error': 'بيانات الدخول غير صحيحة'}), 401

            except Exception as e:
                logger.error(f"[API] خطأ في تسجيل الدخول: {e}")
                return jsonify({'error': 'خطأ في الخادم'}), 500

        @api.route('/analyze', methods=['POST'])
        def analyze_data():
            """تحليل البيانات مع التحليلات التنبؤية"""
            try:
                # التحقق من وجود ملف
                if 'file' not in request.files:
                    return jsonify({'error': 'ملف البيانات مطلوب'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'اسم الملف مطلوب'}), 400

                # Validate file size (max 50MB)
                file.seek(0, os.SEEK_END)
                file_size = file.tell()
                file.seek(0)
                if file_size > 50 * 1024 * 1024:
                    return jsonify({'error': 'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)'}), 400

                # Validate file extension
                allowed_extensions = {'.xlsx', '.xls', '.csv'}
                if not any(file.filename.lower().endswith(ext) for ext in allowed_extensions):
                    return jsonify({'error': f'نوع الملف غير مدعوم. الأنواع المدعومة: {", ".join(allowed_extensions)}'}), 400

                # حفظ الملف مؤقتاً
                temp_path = self.save_temp_file(file)

                # تحليل البيانات الأساسي
                result = self.analyze_file(temp_path, file.filename or "uploaded_file")

                # إضافة التحليلات التنبؤية إذا نجح التحليل الأساسي
                if result.get('success', False):
                    try:
                        from ai_analytics import analyze_data_intelligently
                        intelligent_result = analyze_data_intelligently(result)

                        # دمج النتائج الذكية مع النتائج الأساسية
                        result['intelligent_analysis'] = intelligent_result
                        result['predictive_insights'] = intelligent_result.get('insights', [])
                        result['predictive_recommendations'] = intelligent_result.get('recommendations', [])

                        logger.info("[API] تم إضافة التحليلات التنبؤية بنجاح")
                    except Exception as predictive_error:
                        logger.warning(f"[API] فشل في التحليلات التنبؤية: {predictive_error}")
                        result['predictive_analysis_status'] = 'failed'

                # تنظيف الملف المؤقت
                try:
                    os.remove(temp_path)
                except OSError as e:
                    logger.warning(f"[API] فشل في حذف الملف المؤقت: {e}")

                return jsonify(result)

            except Exception as e:
                logger.error(f"[API] خطأ في تحليل البيانات: {e}")
                return jsonify({'error': 'خطأ في تحليل البيانات'}), 500

        @api.route('/analyze/async', methods=['POST'])
        @self.require_auth
        @self.rate_limit
        def analyze_data_async():
            """تحليل البيانات بشكل غير متزامن"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'ملف البيانات مطلوب'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'اسم الملف مطلوب'}), 400

                # إنشاء معرف مهمة
                task_id = str(uuid.uuid4())

                # حفظ الملف مؤقتاً
                temp_path = self.save_temp_file(file)

                # بدء التحليل في خلفية
                thread = threading.Thread(
                    target=self.analyze_file_async,
                    args=(task_id, temp_path, file.filename)
                )
                thread.daemon = True
                thread.start()

                return jsonify({
                    'task_id': task_id,
                    'status': 'processing',
                    'message': 'تم بدء التحليل'
                })

            except Exception as e:
                logger.error(f"[API] خطأ في التحليل غير المتزامن: {e}")
                return jsonify({'error': 'خطأ في بدء التحليل'}), 500

        @api.route('/analyze/status/<task_id>', methods=['GET'])
        @self.require_auth
        def get_analysis_status(task_id):
            """الحصول على حالة التحليل"""
            # هذا يتطلب نظام تخزين حالة المهام
            # يمكن تنفيذه لاحقاً مع Redis أو قاعدة بيانات
            return jsonify({
                'task_id': task_id,
                'status': 'completed',  # placeholder
                'progress': 100
            })

        @api.route('/analyze/result/<task_id>', methods=['GET'])
        @self.require_auth
        def get_analysis_result(task_id):
            """الحصول على نتيجة التحليل"""
            # placeholder - يتطلب تنفيذ نظام التخزين
            return jsonify({
                'task_id': task_id,
                'status': 'completed',
                'result': {}
            })

        @api.route('/sectors', methods=['GET'])
        @self.require_auth
        @cached(ttl=300)  # Cache for 5 minutes
        def get_supported_sectors():
            """الحصول على القطاعات المدعومة"""
            sectors = {
                'customs': {
                    'name': 'قطاع الجمارك',
                    'description': 'تحليل البيانات الجمركية',
                    'features': ['stakeholders_analysis', 'accuracy_analysis', 'compliance_check']
                },
                'insurance': {
                    'name': 'قطاع التأمين',
                    'description': 'تحليل بيانات التأمين',
                    'features': ['risk_analysis', 'claims_analysis', 'premium_analysis']
                },
                'education': {
                    'name': 'قطاع التعليم',
                    'description': 'تحليل البيانات التعليمية',
                    'features': ['performance_analysis', 'enrollment_analysis', 'graduation_analysis']
                },
                'health': {
                    'name': 'قطاع الصحة',
                    'description': 'تحليل البيانات الصحية',
                    'features': ['patient_analysis', 'treatment_analysis', 'outcome_analysis']
                },
                'finance': {
                    'name': 'قطاع المالية',
                    'description': 'تحليل البيانات المالية',
                    'features': ['transaction_analysis', 'risk_analysis', 'compliance_analysis']
                },
                'hr': {
                    'name': 'قطاع الموارد البشرية',
                    'description': 'تحليل بيانات الموظفين',
                    'features': ['performance_analysis', 'turnover_analysis', 'salary_analysis']
                }
            }

            return jsonify({
                'sectors': sectors,
                'total': len(sectors)
            })

        @api.route('/statistics', methods=['GET'])
        @self.require_auth
        @cached(ttl=60)  # Cache for 1 minute
        def get_system_statistics():
            """الحصول على إحصائيات النظام مع التحليلات التنبؤية"""
            start_time = time.time()
            logger.info("[STATS] Starting system statistics request with predictive analytics")

            try:
                # الحصول على إحصائيات الأداء
                perf_optimizer = get_performance_optimizer()
                stats = perf_optimizer.get_performance_report()

                # Get cache statistics
                cache_manager = get_cache_manager()
                cache_stats = cache_manager.get_stats()

                # إضافة إحصائيات التحليلات التنبؤية
                predictive_stats = self._get_predictive_analytics_stats()

                response_time = time.time() - start_time
                logger.info(f"[STATS] System statistics completed in {response_time:.3f}s")

                return jsonify({
                    'system_stats': stats,
                    'cache_stats': cache_stats,
                    'predictive_stats': predictive_stats,
                    'response_time': round(response_time, 3),
                    'timestamp': datetime.now().isoformat(),
                    'api_version': 'v1.1'  # Updated version with predictive analytics
                })

            except Exception as e:
                response_time = time.time() - start_time
                logger.error(f"[API] خطأ في الحصول على الإحصائيات بعد {response_time:.3f}s: {e}")
                return jsonify({'error': 'خطأ في الحصول على الإحصائيات'}), 500

    def _get_predictive_analytics_stats(self):
        """الحصول على إحصائيات التحليلات التنبؤية"""
        try:
            from ai_analytics import intelligent_analytics

            stats = {
                'ml_availability': True,
                'trained_models_count': len(intelligent_analytics.trained_models) if hasattr(intelligent_analytics, 'trained_models') else 0,
                'predictive_features': [
                    'trend_analysis',
                    'future_predictions',
                    'seasonal_patterns',
                    'clustering_analysis',
                    'risk_predictions'
                ],
                'last_analysis_timestamp': getattr(intelligent_analytics, 'last_analysis_time', None),
                'analysis_count': getattr(intelligent_analytics, 'analysis_count', 0)
            }

            return stats

        except Exception as e:
            logger.warning(f"[PREDICTIVE_STATS] خطأ في الحصول على إحصائيات التحليلات التنبؤية: {e}")
            return {'error': 'فشل في الحصول على إحصائيات التحليلات التنبؤية'}

        @api.route('/export/<session_id>', methods=['GET'])
        @self.require_auth
        def export_analysis(session_id):
            """تصدير نتائج التحليل"""
            try:
                # الحصول على نتائج التحليل من قاعدة البيانات
                db = get_db_manager()
                # تنفيذ استعلام للحصول على النتائج

                return jsonify({
                    'session_id': session_id,
                    'export_formats': ['json', 'csv', 'pdf'],
                    'message': 'تصدير النتائج'
                })

            except Exception as e:
                logger.error(f"[API] خطأ في تصدير النتائج: {e}")
                return jsonify({'error': 'خطأ في تصدير النتائج'}), 500

        @api.route('/export/<format>', methods=['POST'])
        @self.require_auth
        def export_results(format):
            """تصدير النتائج بتنسيق محدد"""
            try:
                # Validate JSON input
                try:
                    data = request.get_json()
                    if not isinstance(data, dict):
                        return jsonify({'error': 'بيانات الطلب يجب أن تكون كائن JSON صحيح'}), 400
                except Exception as e:
                    logger.error(f"[API] خطأ في تحليل JSON: {e}")
                    return jsonify({'error': 'بيانات JSON غير صحيحة'}), 400

                results = data.get('results', {})
                language = data.get('language', 'ar')

                # Validate format parameter
                supported_formats = ['json', 'csv', 'pdf', 'excel']
                if format not in supported_formats:
                    return jsonify({'error': f'تنسيق غير مدعوم. التنسيقات المدعومة: {", ".join(supported_formats)}'}), 400

                # Validate results structure
                if not isinstance(results, dict):
                    return jsonify({'error': 'حقل results يجب أن يكون كائن JSON'}), 400

                # تنفيذ منطق التصدير حسب التنسيق
                if format == 'json':
                    return jsonify(results)
                elif format == 'csv':
                    # تنفيذ تصدير CSV
                    return jsonify({'message': 'CSV export not implemented yet'})
                elif format == 'pdf':
                    # تنفيذ تصدير PDF
                    return jsonify({'message': 'PDF export not implemented yet'})
                elif format == 'excel':
                    # تنفيذ تصدير Excel
                    return jsonify({'message': 'Excel export not implemented yet'})
                else:
                    return jsonify({'error': 'تنسيق غير مدعوم'}), 400

            except Exception as e:
                logger.error(f"[API] خطأ في تصدير النتائج: {e}")
                return jsonify({'error': 'خطأ في تصدير النتائج'}), 500

        @api.route('/export/comprehensive-pdf', methods=['POST'])
        def export_comprehensive_pdf():
            """تصدير تقرير شامل بتنسيق PDF"""
            try:
                data = request.get_json()
                results = data.get('results', {})
                language = data.get('language', 'ar')

                # تنفيذ تصدير PDF الشامل
                return jsonify({'message': 'Comprehensive PDF export not implemented yet'})

            except Exception as e:
                logger.error(f"[API] خطأ في تصدير PDF الشامل: {e}")
                return jsonify({'error': 'خطأ في تصدير PDF الشامل'}), 500

        # Independent Review endpoints for frontend compatibility
        @api.route('/independent-review/preview', methods=['POST'])
        def preview_file():
            """معاينة الملف والحصول على أسماء الأعمدة"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'ملف البيانات مطلوب'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'اسم الملف مطلوب'}), 400

                # حفظ الملف مؤقتاً
                temp_path = self.save_temp_file(file)

                # قراءة أسماء الأعمدة
                import pandas as pd
                if file.filename.lower().endswith('.xlsx'):
                    try:
                        df = pd.read_excel(temp_path, engine='openpyxl')
                    except Exception as e:
                        error_msg = str(e).lower()
                        if 'zip' in error_msg or 'badzipfile' in error_msg or 'not a zip file' in error_msg:
                            raise ValueError("الملف المرفوع ليس ملف Excel صحيح (.xlsx) أو قد يكون تالفاً. يرجى التأكد من نوع الملف ومحاولة إعادة الرفع.")
                        else:
                            raise ValueError(f"خطأ في قراءة ملف Excel: {str(e)}")
                elif file.filename.lower().endswith('.xls'):
                    try:
                        df = pd.read_excel(temp_path, engine='xlrd')
                    except Exception as e:
                        raise ValueError(f"خطأ في قراءة ملف Excel القديم (.xls): {str(e)}")
                else:
                    df = pd.read_csv(temp_path)

                # إصلاح أسماء الأعمدة العربية المعكوسة
                fixed_columns = []
                for col in df.columns:
                    if isinstance(col, str) and any('\u0600' <= char <= '\u06FF' for char in col):
                        fixed_col = fix_arabic_text(col)
                        fixed_columns.append(fixed_col)
                        if fixed_col != col:
                            logger.info(f"[PREVIEW] Fixed Arabic column name: '{col}' -> '{fixed_col}'")
                    else:
                        fixed_columns.append(col)

                # تنظيف الملف المؤقت
                os.remove(temp_path)

                return jsonify({
                    'columns': fixed_columns,
                    'row_count': len(df),
                    'filename': file.filename
                })

            except Exception as e:
                logger.error(f"[API] خطأ في معاينة الملف: {e}")
                return jsonify({'error': 'خطأ في معاينة الملف'}), 500


        @api.route('/independent-review/analyze', methods=['POST'])
        def analyze_text():
            """تحليل الحقول النصية مع التحليلات التنبؤية"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'ملف البيانات مطلوب'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'اسم الملف مطلوب'}), 400

                # حفظ الملف مؤقتاً
                temp_path = self.save_temp_file(file)
                logger.info(f"[TEXT_ANALYSIS] File saved to: {temp_path}")

                if not os.path.exists(temp_path):
                    logger.error(f"[TEXT_ANALYSIS] File not found after save: {temp_path}")
                    return jsonify({'error': 'فشل في حفظ الملف'}), 500

                file_size = os.path.getsize(temp_path)
                logger.info(f"[TEXT_ANALYSIS] File size: {file_size} bytes")

                if file_size == 0:
                    logger.error(f"[TEXT_ANALYSIS] Empty file uploaded")
                    return jsonify({'error': 'الملف فارغ'}), 400

                # قراءة البيانات
                logger.info(f"[TEXT_ANALYSIS] Attempting to read file: {file.filename}")
                import pandas as pd
                if file.filename.lower().endswith('.xlsx'):
                    try:
                        df = pd.read_excel(temp_path, engine='openpyxl')
                    except Exception as e:
                        error_msg = str(e).lower()
                        if 'zip' in error_msg or 'badzipfile' in error_msg or 'not a zip file' in error_msg:
                            raise ValueError("الملف المرفوع ليس ملف Excel صحيح (.xlsx) أو قد يكون تالفاً. يرجى التأكد من نوع الملف ومحاولة إعادة الرفع.")
                        else:
                            raise ValueError(f"خطأ في قراءة ملف Excel: {str(e)}")
                elif file.filename.lower().endswith('.xls'):
                    try:
                        df = pd.read_excel(temp_path, engine='xlrd')
                    except Exception as e:
                        raise ValueError(f"خطأ في قراءة ملف Excel القديم (.xls): {str(e)}")
                else:
                    df = pd.read_csv(temp_path)
                logger.info(f"[TEXT_ANALYSIS] File read successfully, shape: {df.shape}")

                # تحليل الحقول النصية
                logger.info(f"[TEXT_ANALYSIS] Starting text analysis for {len(df.columns)} columns")
                text_analysis = []
                quality_metrics = {
                    'total_text_fields': 0,
                    'high_quality_fields': 0,
                    'medium_quality_fields': 0,
                    'low_quality_fields': 0,
                    'fields_with_nulls': 0,
                    'fields_with_duplicates': 0,
                    'average_quality_score': 0.0
                }

                total_quality_score = 0
                text_columns_found = 0

                for col in df.columns:
                    if df[col].dtype == 'object':  # Text columns
                        text_columns_found += 1
                        logger.info(f"[TEXT_ANALYSIS] Processing text column {text_columns_found}: {col}")
                        quality_metrics['total_text_fields'] += 1

                        # إحصائيات أساسية
                        stats = {
                            'field_name': col,
                            'total_records': len(df),
                            'null_values': df[col].isnull().sum(),
                            'empty_values': (df[col].astype(str).str.strip() == '').sum(),
                            'unique_values': len(df[col].dropna().unique()),
                            'most_frequent': df[col].value_counts().head(5).to_dict() if len(df[col].dropna()) > 0 else {},
                        }

                        # حساب نسبة القيم المكررة
                        if stats['unique_values'] > 0:
                            duplicate_count = len(df) - stats['unique_values']
                            stats['duplicates'] = duplicate_count
                            stats['duplicate_percentage'] = (duplicate_count / len(df)) * 100
                        else:
                            stats['duplicates'] = 0
                            stats['duplicate_percentage'] = 0

                        # كشف القيم الشاذة النصية
                        outliers = detect_text_outliers(df[col], col)
                        stats['outlier_values'] = len(outliers)
                        stats['outlier_percentage'] = (len(outliers) / len(df)) * 100 if len(df) > 0 else 0

                        # حساب جودة الحقل
                        quality_score = 100
                        if stats['null_values'] > 0:
                            quality_score -= min(30, (stats['null_values'] / len(df)) * 100)
                        if stats['duplicates'] > 0:
                            quality_score -= min(20, (stats['duplicates'] / len(df)) * 50)
                        if stats['outlier_values'] > 0:
                            quality_score -= min(15, (stats['outlier_values'] / len(df)) * 30)

                        stats['quality_score'] = max(0, quality_score)
                        total_quality_score += quality_score

                        # تصنيف الجودة
                        if quality_score >= 80:
                            quality_metrics['high_quality_fields'] += 1
                            stats['quality_level'] = 'ممتازة'
                        elif quality_score >= 60:
                            quality_metrics['medium_quality_fields'] += 1
                            stats['quality_level'] = 'جيدة'
                        elif quality_score >= 40:
                            quality_metrics['low_quality_fields'] += 1
                            stats['quality_level'] = 'متوسطة'
                        else:
                            stats['quality_level'] = 'ضعيفة'

                        # تحديث المقاييس
                        if stats['null_values'] > 0:
                            quality_metrics['fields_with_nulls'] += 1
                        if stats['duplicates'] > 0:
                            quality_metrics['fields_with_duplicates'] += 1

                        stats['data_source'] = 'ملف محمل'
                        stats['recommendations'] = self.get_text_recommendations(stats)
                        stats['field_type'] = 'نصي'
                        stats['importance'] = 'متوسطة'  # Default importance
                        stats['repetition_allowed'] = 'نعم' if stats['duplicate_percentage'] < 50 else 'لا'
                        stats['sequence_required'] = 'لا'
                        stats['expected_words'] = 'لا ينطبق'
                        stats['fixed_length'] = 'غير محدد'
                        stats['regex_pattern'] = 'غير محدد'
                        stats['outlier_examples'] = outliers[:5] if outliers else []
                        stats['quality_notes'] = f"جودة {stats['quality_level']} - {stats['quality_score']:.1f}/100"
                        stats['status_code'] = 'OK' if quality_score >= 60 else 'WARNING'
                        stats['recommendation'] = 'تحسين جودة البيانات' if quality_score < 80 else 'لا توجد توصيات'
                        stats['priority'] = 'عالية' if quality_score < 40 else 'متوسطة'
                        stats['data_steward'] = 'غير محدد'
                        stats['last_checked'] = datetime.now().strftime('%Y-%m-%d')
                        stats['cross_field_rules'] = 'لا توجد قواعد'

                        text_analysis.append(stats)

                # حساب متوسط الجودة
                if quality_metrics['total_text_fields'] > 0:
                    quality_metrics['average_quality_score'] = total_quality_score / quality_metrics['total_text_fields']
                    logger.info(f"[TEXT_ANALYSIS] Average quality score: {quality_metrics['average_quality_score']:.2f}")

                logger.info(f"[TEXT_ANALYSIS] Found {text_columns_found} text columns out of {len(df.columns)} total columns")

                # تنظيف الملف المؤقت
                os.remove(temp_path)
                logger.info(f"[TEXT_ANALYSIS] Temporary file cleaned up: {temp_path}")

                result = {
                    'success': True,
                    'field_analysis': text_analysis,
                    'quality_metrics': quality_metrics,
                    'file_info': {
                        'name': file.filename,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'text_columns': quality_metrics['total_text_fields']
                    },
                    'timestamp': datetime.now().isoformat()
                }

                logger.info(f"[TEXT_ANALYSIS] Analysis completed successfully. Returning {len(text_analysis)} field analyses.")
                return jsonify(result)

            except Exception as e:
                logger.error(f"[API] خطأ في تحليل الحقول النصية: {e}")
                return jsonify({'error': 'خطأ في تحليل الحقول النصية', 'details': str(e)}), 500

        @api.route('/api/independent-review/analyze-numeric', methods=['POST'])
        def analyze_numeric():
            """تحليل الحقول الرقمية مع التحليلات التنبؤية"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'ملف البيانات مطلوب'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'اسم الملف مطلوب'}), 400

                # حفظ الملف مؤقتاً
                temp_path = self.save_temp_file(file)
                logger.info(f"[NUMERIC_ANALYSIS] File saved to: {temp_path}")
                if not os.path.exists(temp_path):
                    logger.error(f"[NUMERIC_ANALYSIS] File not found after save: {temp_path}")
                    return jsonify({'error': 'فشل في حفظ الملف'}), 500
            
                file_size = os.path.getsize(temp_path)
                logger.info(f"[NUMERIC_ANALYSIS] File size: {file_size} bytes")
            
                if file_size == 0:
                    logger.error(f"[NUMERIC_ANALYSIS] Empty file uploaded")
                    return jsonify({'error': 'الملف فارغ'}), 400
            
                # قراءة البيانات
                logger.info(f"[NUMERIC_ANALYSIS] Attempting to read file: {file.filename}")
                import pandas as pd
                if file.filename.lower().endswith('.xlsx'):
                    try:
                        df = pd.read_excel(temp_path, engine='openpyxl')
                    except Exception as e:
                        error_msg = str(e).lower()
                        if 'zip' in error_msg or 'badzipfile' in error_msg or 'not a zip file' in error_msg:
                            raise ValueError("الملف المرفوع ليس ملف Excel صحيح (.xlsx) أو قد يكون تالفاً. يرجى التأكد من نوع الملف ومحاولة إعادة الرفع.")
                        else:
                            raise ValueError(f"خطأ في قراءة ملف Excel: {str(e)}")
                elif file.filename.lower().endswith('.xls'):
                    try:
                        df = pd.read_excel(temp_path, engine='xlrd')
                    except Exception as e:
                        raise ValueError(f"خطأ في قراءة ملف Excel القديم (.xls): {str(e)}")
                else:
                    df = pd.read_csv(temp_path)
                logger.info(f"[NUMERIC_ANALYSIS] File read successfully, shape: {df.shape}")

                # تحليل الحقول الرقمية
                logger.info(f"[NUMERIC_ANALYSIS] Starting numeric analysis for {len(df.columns)} columns")
                numeric_analysis = []
                quality_metrics = {
                    'total_numeric_fields': 0,
                    'high_quality_fields': 0,
                    'medium_quality_fields': 0,
                    'low_quality_fields': 0,
                    'fields_with_nulls': 0,
                    'fields_with_outliers': 0,
                    'average_quality_score': 0.0
                }

                total_quality_score = 0
                numeric_columns_found = 0

                for col in df.columns:
                    if df[col].dtype in ['int64', 'float64']:
                        numeric_columns_found += 1
                        logger.info(f"[NUMERIC_ANALYSIS] Processing numeric column {numeric_columns_found}: {col}")
                        quality_metrics['total_numeric_fields'] += 1

                        # إحصائيات أساسية
                        stats = {
                            'field_name': col,
                            'total_records': len(df),
                            'null_values': df[col].isnull().sum(),
                            'zero_values': (df[col] == 0).sum(),
                            'min_value': float(df[col].min()) if not df[col].empty else 0,
                            'max_value': float(df[col].max()) if not df[col].empty else 0,
                            'mean': float(df[col].mean()) if not df[col].empty else 0,
                            'median': float(df[col].median()) if not df[col].empty else 0,
                            'std_dev': float(df[col].std()) if not df[col].empty else 0,
                            'q1': float(df[col].quantile(0.25)) if not df[col].empty else 0,
                            'q3': float(df[col].quantile(0.75)) if not df[col].empty else 0,
                        }
                        logger.info(f"[NUMERIC_ANALYSIS] Basic stats for {col}: min={stats['min_value']}, max={stats['max_value']}, mean={stats['mean']}")

                        # كشف القيم الشاذة
                        if not df[col].empty:
                            iqr = stats['q3'] - stats['q1']
                            lower_bound = stats['q1'] - 1.5 * iqr
                            upper_bound = stats['q3'] + 1.5 * iqr
                            outliers = df[col][(df[col] < lower_bound) | (df[col] > upper_bound)]
                            stats['outliers'] = len(outliers)
                        else:
                            stats['outliers'] = 0

                        # حساب جودة الحقل
                        quality_score = 100
                        if stats['null_values'] > 0:
                            quality_score -= min(30, (stats['null_values'] / len(df)) * 100)
                        if stats['outliers'] > 0:
                            quality_score -= min(20, (stats['outliers'] / len(df)) * 50)
                        if stats['std_dev'] == 0 or pd.isna(stats['std_dev']):
                            quality_score -= 10  # قيم ثابتة

                        stats['quality_score'] = max(0, quality_score)
                        total_quality_score += quality_score

                        # تصنيف الجودة
                        if quality_score >= 80:
                            quality_metrics['high_quality_fields'] += 1
                            stats['quality_level'] = 'ممتازة'
                        elif quality_score >= 60:
                            quality_metrics['medium_quality_fields'] += 1
                            stats['quality_level'] = 'جيدة'
                        elif quality_score >= 40:
                            quality_metrics['low_quality_fields'] += 1
                            stats['quality_level'] = 'متوسطة'
                        else:
                            stats['quality_level'] = 'ضعيفة'

                        # تحديث المقاييس
                        if stats['null_values'] > 0:
                            quality_metrics['fields_with_nulls'] += 1
                        if stats['outliers'] > 0:
                            quality_metrics['fields_with_outliers'] += 1

                        stats['data_source'] = 'ملف محمل'
                        stats['recommendations'] = self.get_numeric_recommendations(stats)

                        # إضافة اقتراحات التنظيف الآلي
                        stats['cleansing_suggestions'] = self._generate_field_cleansing_plan(col, stats, df[col])

                        numeric_analysis.append(stats)

                # حساب متوسط الجودة
                if quality_metrics['total_numeric_fields'] > 0:
                    quality_metrics['average_quality_score'] = total_quality_score / quality_metrics['total_numeric_fields']
                    logger.info(f"[NUMERIC_ANALYSIS] Average quality score: {quality_metrics['average_quality_score']:.2f}")

                logger.info(f"[NUMERIC_ANALYSIS] Found {numeric_columns_found} numeric columns out of {len(df.columns)} total columns")

                # إضافة التحليلات التنبؤية للبيانات الرقمية
                predictive_numeric_analysis = self._add_predictive_numeric_analysis(df, numeric_analysis)

                # إضافة ملخص التنظيف الآلي
                cleansing_summary = self._generate_dataset_cleansing_summary(numeric_analysis, df)

                # تنظيف الملف المؤقت
                os.remove(temp_path)
                logger.info(f"[NUMERIC_ANALYSIS] Temporary file cleaned up: {temp_path}")

                result = {
                    'success': True,
                    'numeric_analysis': numeric_analysis,
                    'predictive_numeric_analysis': predictive_numeric_analysis,
                    'quality_metrics': quality_metrics,
                    'cleansing_summary': cleansing_summary,
                    'file_info': {
                        'name': file.filename,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'numeric_columns': quality_metrics['total_numeric_fields']
                    },
                    'timestamp': datetime.now().isoformat()
                }

                logger.info(f"[NUMERIC_ANALYSIS] Analysis completed successfully. Returning {len(numeric_analysis)} field analyses.")
                return jsonify(result)

            except Exception as e:
                logger.error(f"[API] خطأ في تحليل الحقول الرقمية: {e}")
                return jsonify({'error': 'خطأ في تحليل الحقول الرقمية', 'details': str(e)}), 500

    def _add_predictive_numeric_analysis(self, df, numeric_analysis):
        """إضافة التحليلات التنبؤية للبيانات الرقمية"""
        try:
            from ai_analytics import intelligent_analytics

            # استخراج البيانات الرقمية للتحليل التنبؤي
            numeric_cols = [col for col in df.columns if df[col].dtype in ['int64', 'float64']]
            if len(numeric_cols) > 0 and len(df) > 10:
                numeric_df = df[numeric_cols].dropna()

                # إجراء التحليلات التنبؤية
                predictive_results = intelligent_analytics._perform_predictive_analysis({
                    'original_data': numeric_df.to_dict('records'),
                    'analysis': {'basic_stats': {'total_rows': len(numeric_df)}}
                })

                return predictive_results
            else:
                return {'message': 'البيانات غير كافية للتحليلات التنبؤية'}

        except Exception as e:
            logger.warning(f"[PREDICTIVE_NUMERIC] خطأ في التحليلات التنبؤية الرقمية: {e}")
            return {'error': str(e)}

        @api.route('/independent-review/export/<format>', methods=['POST'])
        @self.require_auth
        def export_independent_review(format):
            """تصدير نتائج المراجعة المستقلة"""
            try:
                # Validate JSON input
                try:
                    data = request.get_json()
                    if not isinstance(data, dict):
                        return jsonify({'error': 'بيانات الطلب يجب أن تكون كائن JSON صحيح'}), 400
                except Exception as e:
                    logger.error(f"[API] خطأ في تحليل JSON: {e}")
                    return jsonify({'error': 'بيانات JSON غير صحيحة'}), 400

                results = data.get('results', {})
                language = data.get('language', 'ar')

                # Validate format parameter
                supported_formats = ['json', 'csv', 'excel']
                if format not in supported_formats:
                    return jsonify({'error': f'تنسيق غير مدعوم. التنسيقات المدعومة: {", ".join(supported_formats)}'}), 400

                # Validate results structure
                if not isinstance(results, dict):
                    return jsonify({'error': 'حقل results يجب أن يكون كائن JSON'}), 400

                # Get uploaded file or use sample data for full data export
                df = None
                uploaded_files = request.files.getlist('files')

                if uploaded_files:
                    # Process uploaded file
                    file = uploaded_files[0]
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    elif file.filename.lower().endswith('.xlsx'):
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    elif file.filename.lower().endswith('.xls'):
                        df = pd.read_excel(file.stream, engine='xlrd')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                else:
                    # Use sample data from uploads directory - try to find the most recent file
                    import os
                    upload_dir = 'uploads'
                    if os.path.exists(upload_dir):
                        sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
                        if sample_files:
                            # Sort by modification time to get the most recent file
                            sample_files.sort(key=lambda x: os.path.getmtime(os.path.join(upload_dir, x)), reverse=True)
                            file_path = os.path.join(upload_dir, sample_files[0])
                            try:
                                if file_path.endswith('.csv'):
                                    df = pd.read_csv(file_path, encoding='utf-8')
                                else:
                                    df = pd.read_excel(file_path, engine='openpyxl')
                                logger.info(f"[EXPORT] Loaded data from file: {file_path}, shape: {df.shape}")
                            except Exception as e:
                                logger.error(f"[EXPORT] Error reading file {file_path}: {e}")
                                return jsonify({'error': f'خطأ في قراءة الملف: {str(e)}'}), 400

                if df is None or df.empty:
                    # If no data files are available, we'll export just the analysis results
                    logger.warning("[EXPORT] No data files available, exporting analysis results only")
                    df = None  # Will handle this case below

                if df is not None:
                    # Fix Arabic text in column names and data
                    df = process_dataframe_arabic_text(df)
                    df.columns = [fix_arabic_text(col) for col in df.columns]
                    logger.info(f"[EXPORT] Data loaded successfully: {df.shape[0]} rows, {df.shape[1]} columns")
                else:
                    logger.info("[EXPORT] Exporting analysis results without original data")

                # تنفيذ منطق التصدير حسب التنسيق
                if format == 'excel':
                    # إنشاء ملف Excel مع جميع البيانات
                    from io import BytesIO

                    output = BytesIO()

                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        # كتابة البيانات الكاملة في ورقة منفصلة (إذا كانت متوفرة)
                        if df is not None:
                            df.to_excel(writer, sheet_name='جميع_البيانات', index=False)

                        # كتابة نتائج التحليل في ورقات منفصلة
                        if 'field_analysis' in results and results['field_analysis']:
                            df_analysis = pd.DataFrame(results['field_analysis'])
                            df_analysis.to_excel(writer, sheet_name='تحليل_الحقول', index=False)

                        if 'numeric_analysis' in results and results['numeric_analysis']:
                            df_numeric = pd.DataFrame(results['numeric_analysis'])
                            df_numeric.to_excel(writer, sheet_name='تحليل_رقمي', index=False)

                        if 'quality_metrics' in results and results['quality_metrics']:
                            df_metrics = pd.DataFrame([results['quality_metrics']])
                            df_metrics.to_excel(writer, sheet_name='مقاييس_الجودة', index=False)

                        # إذا لم تكن هناك بيانات أصلية، أضف ورقة توضيحية
                        if df is None:
                            import pandas as pd
                            info_df = pd.DataFrame({
                                'معلومات': ['تم تصدير نتائج التحليل فقط - البيانات الأصلية غير متوفرة',
                                          f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                                          f'عدد الحقول المحللة: {len(results.get("field_analysis", []))}']
                            })
                            info_df.to_excel(writer, sheet_name='معلومات_التصدير', index=False)

                    output.seek(0)

                    return send_file(
                        output,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        as_attachment=True,
                        download_name=f'analysis_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                    )

                elif format == 'json':
                    # إرجاع البيانات الكاملة مع نتائج التحليل
                    if df is not None:
                        complete_data = {
                            'data': df.to_dict('records'),
                            'columns': list(df.columns),
                            'row_count': len(df),
                            'analysis_results': results,
                            'export_timestamp': datetime.now().isoformat()
                        }
                    else:
                        # إذا لم تكن هناك بيانات أصلية، أعد فقط نتائج التحليل
                        complete_data = {
                            'data': [],
                            'columns': [],
                            'row_count': 0,
                            'analysis_results': results,
                            'export_timestamp': datetime.now().isoformat(),
                            'note': 'البيانات الأصلية غير متوفرة - تم تصدير نتائج التحليل فقط'
                        }
                    return jsonify(complete_data)

                elif format == 'csv':
                    # تصدير البيانات الكاملة كـ CSV
                    csv_output = StringIO()

                    if df is not None:
                        df.to_csv(csv_output, index=False, encoding='utf-8-sig')
                    else:
                        # إذا لم تكن هناك بيانات أصلية، أضف معلومات التحليل
                        import csv
                        writer = csv.writer(csv_output)
                        writer.writerow(['معلومات التصدير', 'القيمة'])
                        writer.writerow(['تاريخ التصدير', datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
                        writer.writerow(['البيانات الأصلية', 'غير متوفرة'])
                        writer.writerow(['', ''])

                        # أضف نتائج التحليل
                        if 'field_analysis' in results and results['field_analysis']:
                            writer.writerow(['تحليل الحقول', ''])
                            writer.writerow(['اسم الحقل', 'القيم المكررة', 'إجمالي السجلات'])
                            for field in results['field_analysis']:
                                writer.writerow([
                                    field.get('field_name', ''),
                                    field.get('duplicates', 0),
                                    field.get('total_records', 0)
                                ])

                    csv_output.seek(0)

                    return send_file(
                        BytesIO(csv_output.getvalue().encode('utf-8-sig')),
                        mimetype='text/csv',
                        as_attachment=True,
                        download_name=f'analysis_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                    )

                return jsonify({'error': f'تنسيق التصدير غير مدعوم: {format}'}), 400

            except Exception as e:
                logger.error(f"[API] خطأ في تصدير النتائج: {e}")
                return jsonify({'error': 'خطأ في تصدير النتائج'}), 500

        @api.route('/independent-review/export/comprehensive-pdf', methods=['POST'])
        def export_comprehensive_pdf_independent():
            """تصدير تقرير شامل كـ PDF للمراجعة المستقلة"""
            try:
                data = request.get_json()
                results = data.get('results', data.get('analysisResults', {}))

                # إنشاء تقرير PDF شامل
                # (يمكن تنفيذ منطق إنشاء PDF هنا لاحقاً)

                return jsonify({
                    'message': 'تقرير PDF الشامل جاهز للتحميل',
                    'status': 'success'
                })

            except Exception as e:
                logger.error(f"[API] خطأ في إنشاء تقرير PDF: {e}")
                return jsonify({'error': 'خطأ في إنشاء التقرير'}), 500

    def get_numeric_recommendations(self, stats):
        """الحصول على توصيات للحقول الرقمية"""
        recommendations = []

        try:
            if stats.get('null_values', 0) > 0:
                null_percentage = (stats['null_values'] / stats.get('total_records', 1)) * 100
                if null_percentage > 20:
                    recommendations.append('نسبة القيم الناقصة عالية - يُنصح بتنظيف البيانات')
                else:
                    recommendations.append('توجد قيم ناقصة - يُنصح بمعالجتها')

            if stats.get('outliers', 0) > 0:
                outlier_percentage = (stats['outliers'] / stats.get('total_records', 1)) * 100
                if outlier_percentage > 10:
                    recommendations.append('نسبة القيم الشاذة عالية - يُنصح بمراجعة قواعد الكشف عن الشاذات')
                else:
                    recommendations.append('توجد قيم شاذة - يُنصح بالتحقق من صحتها')

            if stats.get('std_dev', 1) == 0 or (isinstance(stats.get('std_dev'), float) and (stats['std_dev'] != stats['std_dev'])):  # Check for NaN
                recommendations.append('القيم ثابتة - تحقق من صحة البيانات')

            if stats.get('quality_score', 100) < 60:
                recommendations.append('جودة البيانات منخفضة - يُنصح بإجراء تنظيف شامل')

            if len(recommendations) == 0:
                recommendations.append('جودة البيانات جيدة - لا توجد مشاكل بارزة')

        except (KeyError, TypeError, ZeroDivisionError) as e:
            logger.warning(f"[API] خطأ في حساب التوصيات: {e}")
            recommendations.append('تعذر حساب التوصيات - تحقق من صحة البيانات')

        return recommendations
    
    def _generate_field_cleansing_plan(self, field_name, stats, series):
        """توليد خطة تنظيف لحقل محدد"""
        try:
            plan = []

            # خطة معالجة البيانات المفقودة
            if stats.get('null_values', 0) > 0:
                null_pct = (stats['null_values'] / stats.get('total_records', 1)) * 100
                if null_pct <= 30:
                    plan.append({
                        'action': 'fill_missing',
                        'method': 'mean_median',
                        'confidence': 85,
                        'impact': f"تعبئة {null_pct:.1f}% من القيم المفقودة"
                    })

            # خطة معالجة القيم الشاذة
            if stats.get('outliers', 0) > 0:
                outlier_pct = (stats['outliers'] / stats.get('total_records', 1)) * 100
                if outlier_pct <= 15:
                    plan.append({
                        'action': 'handle_outliers',
                        'method': 'iqr_method',
                        'confidence': 75,
                        'impact': f"معالجة {stats['outliers']} قيمة شاذة"
                    })

            # خطة توحيد البيانات
            if stats.get('quality_score', 100) < 90:
                plan.append({
                    'action': 'standardize_values',
                    'method': 'remove_whitespace',
                    'confidence': 95,
                    'impact': 'توحيد تنسيق القيم'
                })

            return plan[:3]  # حد أقصى 3 اقتراحات

        except Exception as e:
            logger.warning(f"[CLEANSING_PLAN] خطأ في توليد خطة التنظيف لحقل {field_name}: {e}")
            return []
    
    def _preprocess_dataframe(self, df):
        """تنظيف وتحضير البيانات الأولي"""
        try:
            import pandas as pd

            # إنشاء نسخة من البيانات لتجنب التعديل على الأصل
            df_processed = df.copy()

            # تنظيف أسماء الأعمدة
            df_processed.columns = df_processed.columns.str.strip()

            # إصلاح النصوص العربية في أسماء الأعمدة
            df_processed.columns = [fix_arabic_text(col) for col in df_processed.columns]

            # تنظيف البيانات النصية
            for col in df_processed.select_dtypes(include=['object']).columns:
                # إزالة المسافات الزائدة
                df_processed[col] = df_processed[col].astype(str).str.strip()

                # إصلاح النصوص العربية المعكوسة
                df_processed[col] = df_processed[col].apply(fix_arabic_text)

                # تحويل القيم الفارغة إلى NaN
                df_processed[col] = df_processed[col].replace(['', 'nan', 'NaN', 'null', 'NULL'], pd.NA)

            # تنظيف البيانات الرقمية
            for col in df_processed.select_dtypes(include=['number']).columns:
                # تحويل القيم غير الرقمية إلى NaN
                df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')

            # إزالة الصفوف المكررة تماماً
            initial_rows = len(df_processed)
            df_processed = df_processed.drop_duplicates()
            duplicates_removed = initial_rows - len(df_processed)

            if duplicates_removed > 0:
                logger.info(f"[PREPROCESS] Removed {duplicates_removed} duplicate rows")

            # إزالة الأعمدة الفارغة تماماً
            empty_columns = df_processed.columns[df_processed.isnull().all()]
            if len(empty_columns) > 0:
                df_processed = df_processed.drop(columns=empty_columns)
                logger.info(f"[PREPROCESS] Removed {len(empty_columns)} empty columns: {list(empty_columns)}")

            logger.info(f"[PREPROCESS] Data preprocessing completed. Shape: {df_processed.shape}")
            return df_processed

        except Exception as e:
            logger.error(f"[PREPROCESS] Error in data preprocessing: {e}")
            # إرجاع البيانات الأصلية في حالة فشل المعالجة
            return df

    def _detect_column_types_enhanced(self, df):
        """كشف محسن لأنواع الأعمدة"""
        try:
            column_types = {}

            for col in df.columns:
                # فحص إذا كان العمود رقمي
                if df[col].dtype in ['int64', 'float64']:
                    # التحقق من نسبة القيم غير الفارغة
                    non_null_ratio = df[col].notna().sum() / len(df)
                    if non_null_ratio > 0.5:  # أكثر من 50% من القيم غير فارغة
                        column_types[col] = 'number'
                    else:
                        column_types[col] = 'text'  # عمود رقمي لكنه يحتوي على الكثير من القيم الفارغة
                else:
                    # فحص إذا كان العمود يحتوي على تواريخ
                    try:
                        pd.to_datetime(df[col], errors='raise')
                        column_types[col] = 'date'
                    except (ValueError, TypeError):
                        # فحص إذا كان العمود يحتوي على قيم منطقية
                        unique_vals = df[col].dropna().unique()
                        if len(unique_vals) <= 10 and all(isinstance(val, (bool, str)) for val in unique_vals if pd.notna(val)):
                            # فحص إذا كانت القيم تبدو منطقية
                            bool_like = any(str(val).lower() in ['true', 'false', '1', '0', 'yes', 'no', 'نعم', 'لا'] for val in unique_vals)
                            if bool_like:
                                column_types[col] = 'boolean'
                            else:
                                column_types[col] = 'category'
                        else:
                            column_types[col] = 'text'

            logger.info(f"[COLUMN_TYPES] Detected types for {len(column_types)} columns")
            return column_types

        except Exception as e:
            logger.error(f"[COLUMN_TYPES] Error detecting column types: {e}")
            # إرجاع أنواع افتراضية
            return {col: 'text' for col in df.columns}

    def _enhance_analysis_results(self, df, analysis_result, column_types):
        """تحسين نتائج التحليل بمعلومات إضافية"""
        try:
            enhanced = analysis_result.copy() if analysis_result else {}

            # إضافة إحصائيات عامة
            enhanced['general_stats'] = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'numeric_columns': sum(1 for t in column_types.values() if t == 'number'),
                'text_columns': sum(1 for t in column_types.values() if t == 'text'),
                'date_columns': sum(1 for t in column_types.values() if t == 'date'),
                'boolean_columns': sum(1 for t in column_types.values() if t == 'boolean'),
                'category_columns': sum(1 for t in column_types.values() if t == 'category'),
                'null_percentage': round(df.isnull().sum().sum() / (len(df) * len(df.columns)) * 100, 2),
                'duplicate_rows': len(df) - len(df.drop_duplicates())
            }

            # إضافة معلومات عن الأعمدة
            enhanced['column_info'] = {}
            for col, col_type in column_types.items():
                col_info = {
                    'type': col_type,
                    'null_count': df[col].isnull().sum(),
                    'null_percentage': round(df[col].isnull().sum() / len(df) * 100, 2),
                    'unique_values': len(df[col].dropna().unique())
                }

                if col_type == 'number':
                    col_info.update({
                        'min': float(df[col].min()) if not df[col].empty else None,
                        'max': float(df[col].max()) if not df[col].empty else None,
                        'mean': float(df[col].mean()) if not df[col].empty else None,
                        'median': float(df[col].median()) if not df[col].empty else None
                    })

                enhanced['column_info'][col] = col_info

            logger.info(f"[ENHANCE] Enhanced analysis results with {len(enhanced.get('column_info', {}))} column details")
            return enhanced

        except Exception as e:
            logger.error(f"[ENHANCE] Error enhancing analysis results: {e}")
            return analysis_result or {}

    def _calculate_overall_data_quality(self, df, enhanced_analysis):
        """حساب جودة البيانات الشاملة"""
        try:
            quality_score = 100

            # عامل نسبة القيم الفارغة
            null_percentage = df.isnull().sum().sum() / (len(df) * len(df.columns)) * 100
            if null_percentage > 20:
                quality_score -= min(30, null_percentage - 20)
            elif null_percentage > 10:
                quality_score -= min(15, null_percentage - 10)

            # عامل الصفوف المكررة
            duplicate_percentage = (len(df) - len(df.drop_duplicates())) / len(df) * 100
            if duplicate_percentage > 5:
                quality_score -= min(20, duplicate_percentage * 2)

            # عامل تنوع البيانات
            column_info = enhanced_analysis.get('column_info', {})
            low_diversity_penalty = 0
            for col, info in column_info.items():
                unique_ratio = info.get('unique_values', 0) / len(df)
                if unique_ratio < 0.01:  # أقل من 1% تنوع
                    low_diversity_penalty += 5

            quality_score -= min(20, low_diversity_penalty)

            # عامل جودة الأعمدة الرقمية
            numeric_columns = [col for col, info in column_info.items() if info.get('type') == 'number']
            if numeric_columns:
                numeric_quality_penalty = 0
                for col in numeric_columns:
                    col_info = column_info[col]
                    if col_info.get('null_percentage', 0) > 50:
                        numeric_quality_penalty += 10

                quality_score -= min(15, numeric_quality_penalty)

            return max(0, round(quality_score, 1))

        except Exception as e:
            logger.error(f"[QUALITY] Error calculating data quality: {e}")
            return 50  # قيمة افتراضية متوسطة

    def _get_quality_grade(self, score):
        """تحديد درجة الجودة بناءً على النقاط"""
        if score >= 90:
            return 'ممتازة'
        elif score >= 80:
            return 'جيدة جداً'
        elif score >= 70:
            return 'جيدة'
        elif score >= 60:
            return 'مقبولة'
        elif score >= 50:
            return 'ضعيفة'
        else:
            return 'سيئة جداً'

    def _generate_quality_recommendations(self, df, enhanced_analysis):
        """توليد توصيات لتحسين جودة البيانات"""
        try:
            recommendations = []

            # فحص نسبة القيم الفارغة
            null_percentage = df.isnull().sum().sum() / (len(df) * len(df.columns)) * 100
            if null_percentage > 20:
                recommendations.append('نسبة القيم الفارغة عالية - يُنصح بتنظيف البيانات أو إعادة جمعها')
            elif null_percentage > 10:
                recommendations.append('توجد قيم فارغة - يُنصح بمعالجتها')

            # فحص الصفوف المكررة
            duplicate_count = len(df) - len(df.drop_duplicates())
            if duplicate_count > len(df) * 0.05:
                recommendations.append(f'توجد {duplicate_count} صف مكرر - يُنصح بإزالتها')

            # فحص تنوع البيانات
            column_info = enhanced_analysis.get('column_info', {})
            low_diversity_cols = []
            for col, info in column_info.items():
                unique_ratio = info.get('unique_values', 0) / len(df)
                if unique_ratio < 0.01:
                    low_diversity_cols.append(col)

            if low_diversity_cols:
                recommendations.append(f'الأعمدة ذات التنوع المنخفض: {", ".join(low_diversity_cols[:3])} - قد تحتاج إلى مراجعة')

            # فحص الأعمدة الرقمية
            numeric_issues = []
            for col, info in column_info.items():
                if info.get('type') == 'number' and info.get('null_percentage', 0) > 50:
                    numeric_issues.append(col)

            if numeric_issues:
                recommendations.append(f'الأعمدة الرقمية ذات القيم الفارغة العالية: {", ".join(numeric_issues[:3])}')

            if not recommendations:
                recommendations.append('جودة البيانات جيدة - لا توجد مشاكل بارزة')

            return recommendations[:5]  # حد أقصى 5 توصيات

        except Exception as e:
            logger.error(f"[RECOMMENDATIONS] Error generating quality recommendations: {e}")
            return ['تعذر توليد التوصيات - تحقق من صحة البيانات']

    def _detect_file_encoding(self, file_path):
        """كشف ترميز الملف"""
        try:
            import chardet

            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # قراءة أول 10KB
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)

                logger.info(f"[ENCODING] Detected encoding: {encoding} (confidence: {confidence:.2f})")
                return encoding

        except ImportError:
            logger.warning("[ENCODING] chardet not available, using utf-8")
            return 'utf-8'
        except Exception as e:
            logger.error(f"[ENCODING] Error detecting file encoding: {e}")
            return 'utf-8'

    def _generate_dataset_cleansing_summary(self, numeric_analysis, df):
        """توليد ملخص التنظيف للمجموعة البيانات"""
        try:
            summary = {
                'total_fields': len(numeric_analysis),
                'fields_needing_attention': 0,
                'automated_actions': 0,
                'manual_actions': 0,
                'estimated_time_minutes': 0,
                'cleansing_priority': 'low'
            }

            for field_stats in numeric_analysis:
                quality_score = field_stats.get('quality_score', 100)
                if quality_score < 80:
                    summary['fields_needing_attention'] += 1

                # حساب الإجراءات التلقائية واليدوية
                cleansing_suggestions = field_stats.get('cleansing_suggestions', [])
                for suggestion in cleansing_suggestions:
                    if suggestion.get('confidence', 0) >= 80:
                        summary['automated_actions'] += 1
                    else:
                        summary['manual_actions'] += 1

            # تقدير الوقت
            summary['estimated_time_minutes'] = (summary['automated_actions'] * 2) + (summary['manual_actions'] * 10)

            # تحديد الأولوية
            if summary['fields_needing_attention'] > summary['total_fields'] * 0.5:
                summary['cleansing_priority'] = 'high'
            elif summary['fields_needing_attention'] > summary['total_fields'] * 0.25:
                summary['cleansing_priority'] = 'medium'

            return summary

        except Exception as e:
            logger.warning(f"[CLEANSING_SUMMARY] خطأ في توليد ملخص التنظيف: {e}")
            return {
                'total_fields': len(numeric_analysis),
                'fields_needing_attention': 0,
                'automated_actions': 0,
                'manual_actions': 0,
                'estimated_time_minutes': 0,
                'cleansing_priority': 'unknown'
            }




        @api.route('/independent-review/export/length-irregularities/', methods=['POST'])
        @self.require_auth
        def export_length_irregularities():
            """Export length irregularities details as Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                if not data or 'indicator_details' not in data:
                    return jsonify({'error': 'No indicator details provided'}), 400

                indicator_details = data['indicator_details']

                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)
                worksheet = workbook.add_worksheet('تفاصيل الطول غير المنتظم')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})

                # Write summary information
                if 'summary' in indicator_details:
                    summary = indicator_details['summary']
                    worksheet.write(0, 0, 'ملخص الطول غير المنتظم', header_format)
                    worksheet.write(1, 0, f"القيم غير المنتظمة: {summary.get('total_irregular_lengths', summary.get('total_input_gaps', 0))}", data_format)
                    worksheet.write(2, 0, f"الحقول المتأثرة: {summary.get('affected_fields', 0)}", data_format)
                    worksheet.write(3, 0, f"تاريخ التصدير: {summary.get('export_timestamp', '')}", data_format)

                # Write field details
                if 'field_details' in indicator_details and indicator_details['field_details']:
                    worksheet.write(6, 0, 'تفاصيل الحقول', header_format)
                    headers = ['اسم الحقل', 'القيم غير المنتظمة', 'النسبة المئوية', 'مستوى المخاطر']
                    for col, header in enumerate(headers):
                        worksheet.write(7, col, header, header_format)

                    row = 8
                    for field in indicator_details['field_details']:
                        worksheet.write(row, 0, field.get('field_name', ''), data_format)
                        worksheet.write(row, 1, field.get('irregular_count', field.get('null_values', 0)), data_format)
                        worksheet.write(row, 2, field.get('percentage', '0%'), data_format)
                        worksheet.write(row, 3, field.get('risk_level', 'منخفض'), data_format)
                        row += 1

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'length_irregularities_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Length irregularities export error: {str(e)}")
                return jsonify({'error': f'Length irregularities export failed: {str(e)}'}), 500

        @api.route('/independent-review/export/indicator-details/', methods=['POST'])
        @self.require_auth
        def export_indicator_details():
            """Export indicator details as Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                if not data or 'indicator_details' not in data:
                    return jsonify({'error': 'No indicator details provided'}), 400

                indicator_details = data['indicator_details']

                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)
                worksheet = workbook.add_worksheet('تفاصيل المؤشر')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})

                # Write summary information
                if 'summary' in indicator_details:
                    summary = indicator_details['summary']
                    worksheet.write(0, 0, 'ملخص المؤشر', header_format)
                    worksheet.write(1, 0, f"المؤشر: {summary.get('indicator', '')}", data_format)
                    worksheet.write(2, 0, f"القيمة: {summary.get('total_input_gaps', summary.get('total_irregular_repetition', summary.get('total_problematic_fields', 0)))}", data_format)
                    worksheet.write(3, 0, f"الحقول المتأثرة: {summary.get('affected_fields', 0)}", data_format)
                    worksheet.write(4, 0, f"تاريخ التصدير: {summary.get('export_timestamp', '')}", data_format)

                # Write field details
                if 'field_details' in indicator_details and indicator_details['field_details']:
                    worksheet.write(6, 0, 'تفاصيل الحقول', header_format)
                    headers = ['اسم الحقل', 'القيم الناقصة', 'القيم الشاذة', 'القيم المكررة', 'النسبة المئوية', 'مستوى المخاطر']
                    for col, header in enumerate(headers):
                        worksheet.write(7, col, header, header_format)

                    row = 8
                    for field in indicator_details['field_details']:
                        worksheet.write(row, 0, field.get('field_name', ''), data_format)
                        worksheet.write(row, 1, field.get('null_values', field.get('duplicate_values', 0)), data_format)
                        worksheet.write(row, 2, field.get('outlier_values', 0), data_format)
                        worksheet.write(row, 3, field.get('duplicate_values', 0), data_format)
                        worksheet.write(row, 4, field.get('percentage', '0%'), data_format)
                        worksheet.write(row, 5, field.get('risk_level', 'منخفض'), data_format)
                        row += 1

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'indicator_details_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Indicator details export error: {str(e)}")
                return jsonify({'error': f'Indicator details export failed: {str(e)}'}), 500

        @api.route('/independent-review/export/excel', methods=['POST'])
        @self.require_auth
        def export_independent_review_excel():
            """Export independent review results as Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                if not data or 'results' not in data:
                    return jsonify({'error': 'No results data provided'}), 400

                results = data['results']

                # Handle different data structures - check if analysis is nested
                if 'analysis' in results and isinstance(results['analysis'], dict):
                    analysis_data = results['analysis']
                    field_analysis = analysis_data.get('field_analysis', [])
                    quality_metrics = results.get('data_quality', {}).get('overall_score', {})
                else:
                    # Fallback to direct access
                    field_analysis = results.get('field_analysis', [])
                    quality_metrics = results.get('quality_metrics', {})

                # Ensure field_analysis is a list
                if not isinstance(field_analysis, list):
                    field_analysis = []

                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)
                worksheet = workbook.add_worksheet('تحليل الحقول')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})

                # Quality metrics - handle different structures
                worksheet.write(0, 0, 'مؤشرات جودة البيانات', header_format)

                # Try to extract quality metrics from different possible locations
                metrics_data = {}
                if isinstance(quality_metrics, dict):
                    # If quality_metrics is a dict, use it directly
                    metrics_data = quality_metrics
                elif 'data_quality' in results and isinstance(results['data_quality'], dict):
                    # If data_quality exists, use it
                    metrics_data = results['data_quality']
                else:
                    # Fallback to empty metrics
                    metrics_data = {
                        'irregular_repetition': 0,
                        'input_gaps': 0,
                        'word_gaps': 0,
                        'length_irregularities': 0,
                        'sequence_irregularities': 0,
                        'outlier_values': 0
                    }

                metrics = [
                    ['التكرار غير المنطقي', metrics_data.get('irregular_repetition', 0)],
                    ['نواقص الإدخال', metrics_data.get('input_gaps', 0)],
                    ['نواقص الكلمات', metrics_data.get('word_gaps', 0)],
                    ['اختلالات الخانات', metrics_data.get('length_irregularities', 0)],
                    ['اختلالات التسلسل', metrics_data.get('sequence_irregularities', 0)],
                    ['النتائج الشاذة', metrics_data.get('outlier_values', 0)]
                ]

                for i, (metric, value) in enumerate(metrics):
                    worksheet.write(i + 1, 0, metric, data_format)
                    worksheet.write(i + 1, 1, value, data_format)

                # Field analysis
                worksheet.write(9, 0, 'تحليل الحقول النصية', header_format)
                headers = ['اسم الحقل', 'نوع الحقل', 'مستوى الأهمية', 'شروط التكرار', 'إجمالي السجلات', 'عدد القيم الناقصة', 'عدد القيم الشاذة', 'عدد القيم المكررة', 'عدد القيم الفريدة', 'عدد الخانات الثابتة', 'نمط متوقع', 'أمثلة على القيم الشاذة', 'ملاحظات الجودة', 'رمز الحالة الآلي', 'التوصية', 'أولوية المعالجة', 'مصدر البيانات', 'مسؤول البيانات', 'آخر تحديث', 'قواعد التحقق المتقاطعة']

                for col, header in enumerate(headers):
                    worksheet.write(9, col, header, header_format)

                # Handle field analysis data - ensure it's properly structured
                if field_analysis and len(field_analysis) > 0:
                    for row, field in enumerate(field_analysis, start=10):
                        # Ensure field is a dictionary
                        if not isinstance(field, dict):
                            continue

                        data_row = [
                            field.get('field_name', ''),
                            field.get('field_type', ''),
                            field.get('importance', ''),
                            field.get('repetition_allowed', ''),
                            field.get('total_records', 0),
                            field.get('null_values', 0),
                            field.get('outlier_values', 0),
                            field.get('duplicates', 0),
                            field.get('unique_values', 0),
                            field.get('fixed_length', 'غير محدد'),
                            field.get('regex_pattern', 'غير محدد'),
                            field.get('outlier_examples', 'لا توجد أمثلة'),
                            field.get('quality_notes', ''),
                            field.get('status_code', 'OK'),
                            field.get('recommendation', 'لا توجد توصيات'),
                            field.get('priority', 'متوسطة'),
                            field.get('data_source', ''),
                            field.get('data_steward', ''),
                            field.get('last_checked', ''),
                            field.get('cross_field_rules', '')
                        ]
                        for col, value in enumerate(data_row):
                            worksheet.write(row, col, value, data_format)
                else:
                    # If no field analysis data, add a note
                    worksheet.write(10, 0, 'لا توجد بيانات تحليل الحقول', data_format)

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'independent_review_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Excel export error: {str(e)}")
                return jsonify({'error': f'Excel export failed: {str(e)}'}), 500

        @api.route('/independent-review/export/duplicates/<field_name>', methods=['POST'])
        @self.require_auth
        def export_duplicates(field_name):
            """Export duplicate values for a specific field to Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                duplicate_details = data.get('duplicate_details') if data else None

                if not duplicate_details:
                    return jsonify({'error': 'No duplicate details provided'}), 400

                # Try to get data from uploaded files first
                df = None
                uploaded_files = request.files.getlist('files')

                if uploaded_files:
                    # Process uploaded file
                    file = uploaded_files[0]
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    elif file.filename.lower().endswith('.xlsx'):
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    elif file.filename.lower().endswith('.xls'):
                        df = pd.read_excel(file.stream, engine='xlrd')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                else:
                    # Try to find recent uploaded files in uploads directory
                    import os
                    upload_dir = 'uploads'
                    if os.path.exists(upload_dir):
                        sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
                        if sample_files:
                            # Sort by modification time to get the most recent file
                            sample_files.sort(key=lambda x: os.path.getmtime(os.path.join(upload_dir, x)), reverse=True)
                            file_path = os.path.join(upload_dir, sample_files[0])
                            try:
                                if file_path.endswith('.csv'):
                                    df = pd.read_csv(file_path, encoding='utf-8')
                                else:
                                    df = pd.read_excel(file_path, engine='openpyxl')
                                logger.info(f"[EXPORT] Loaded data from file: {file_path}, shape: {df.shape}")
                            except Exception as e:
                                logger.error(f"[EXPORT] Error reading file {file_path}: {e}")
                                return jsonify({'error': f'خطأ في قراءة الملف: {str(e)}'}), 400

                if df is None or df.empty:
                    # If no data files are available, return error - we need original data for complete export
                    logger.warning("[EXPORT] No data files available for duplicate export - original data required")
                    return jsonify({'error': 'لا توجد بيانات متاحة للتصدير - يرجى رفع الملف مرة أخرى للحصول على تصدير كامل'}), 400

                # Fix Arabic text in column names
                df = process_dataframe_arabic_text(df)
                df.columns = [fix_arabic_text(col) for col in df.columns]

                # Check if field exists
                if field_name not in df.columns:
                    # Try partial match
                    matching_cols = [col for col in df.columns if field_name.lower() in col.lower()]
                    if matching_cols:
                        field_name = matching_cols[0]
                        logger.info(f"Found matching field: {field_name}")
                    else:
                        return jsonify({
                            'error': f'الحقل غير موجود: {field_name}',
                            'available_fields': list(df.columns)
                        }), 400

                # Use the passed duplicate_details instead of recomputing
                # duplicate_details contains the complete information about duplicates
                if not duplicate_details or not isinstance(duplicate_details, dict):
                    return jsonify({'error': 'بيانات التكرار غير صحيحة'}), 400

                # Extract duplicate values from the passed details
                duplicate_values = list(duplicate_details.keys())
                if not duplicate_values:
                    return jsonify({'error': f'لا توجد قيم متكررة في الحقل: {field_name}'}), 400

                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)

                # Create three worksheets: duplicates summary, duplicate rows only, full data with highlights
                summary_sheet = workbook.add_worksheet('البيانات المتكررة')
                duplicate_rows_sheet = workbook.add_worksheet('الصفوف المتكررة فقط')
                full_data_sheet = workbook.add_worksheet('جميع البيانات')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})
                duplicate_format = workbook.add_format({'bg_color': '#FFFF99', 'border': 1, 'text_wrap': True})  # Light yellow for duplicates

                # Summary sheet
                summary_sheet.write(0, 0, 'القيمة المتكررة', header_format)
                summary_sheet.write(0, 1, 'عدد التكرار', header_format)
                summary_sheet.write(0, 2, 'الصفوف', header_format)

                # Write duplicate data
                row = 1
                for value, count in duplicates.items():
                    # Get row indices for this value
                    row_indices = df[df[field_name] == value].index.tolist()
                    row_indices_str = ', '.join([str(idx + 1) for idx in row_indices])  # +1 for 1-based indexing

                    summary_sheet.write(row, 0, str(value), data_format)
                    summary_sheet.write(row, 1, int(count), data_format)
                    summary_sheet.write(row, 2, row_indices_str, data_format)
                    row += 1

                # Add summary information
                summary_format = workbook.add_format({'bold': True, 'bg_color': '#E6E6FA', 'border': 1})
                summary_sheet.write(row + 1, 0, 'ملخص الحقل', summary_format)
                summary_sheet.write(row + 2, 0, f'إجمالي القيم المتكررة: {len(duplicates)}', data_format)
                summary_sheet.write(row + 3, 0, f'إجمالي التكرارات: {duplicates.sum()}', data_format)
                summary_sheet.write(row + 4, 0, f'إجمالي السجلات: {len(df)}', data_format)

                summary_sheet.set_column(0, 0, 30)
                summary_sheet.set_column(1, 1, 15)
                summary_sheet.set_column(2, 2, 50)

                # Duplicate rows only sheet - export only the rows that contain duplicate values
                # Write headers
                for col_idx, col_name in enumerate(df.columns):
                    duplicate_rows_sheet.write(0, col_idx, col_name, header_format)

                # Get all rows that have duplicate values in the specified field using passed details
                duplicate_row_indices = []
                for dup_info in duplicate_details.values():
                    duplicate_row_indices.extend(dup_info.get('row_indices', []))

                # Remove duplicates and sort
                duplicate_row_indices = sorted(list(set(duplicate_row_indices)))

                # Get duplicate rows
                duplicate_rows = df.loc[duplicate_row_indices]
                duplicate_rows = duplicate_rows.sort_values(by=[field_name])  # Sort by the field to group duplicates

                # Write duplicate rows
                for row_idx, (_, row_data) in enumerate(duplicate_rows.iterrows(), start=1):
                    for col_idx, (col_name, cell_value) in enumerate(row_data.items()):
                        if col_name == field_name:
                            # Highlight the duplicate value
                            duplicate_rows_sheet.write(row_idx, col_idx, str(cell_value), duplicate_format)
                        else:
                            duplicate_rows_sheet.write(row_idx, col_idx, str(cell_value), data_format)

                # Set column widths for duplicate rows sheet
                for col_idx, col_name in enumerate(df.columns):
                    max_width = max(len(str(col_name)), 10)  # Minimum width of 10
                    duplicate_rows_sheet.set_column(col_idx, col_idx, min(max_width, 50))  # Max width of 50

                # Full data sheet - export all rows and columns with duplicates highlighted
                # Write headers
                for col_idx, col_name in enumerate(df.columns):
                    full_data_sheet.write(0, col_idx, col_name, header_format)

                # Write data rows
                for row_idx, (_, row_data) in enumerate(df.iterrows(), start=1):
                    # Check if this row's field value is a duplicate using passed details
                    field_value = row_data[field_name]
                    is_duplicate = str(field_value) in duplicate_details

                    for col_idx, (col_name, cell_value) in enumerate(row_data.items()):
                        if is_duplicate and col_name == field_name:
                            # Highlight the duplicate value in the specific field
                            full_data_sheet.write(row_idx, col_idx, str(cell_value), duplicate_format)
                        else:
                            full_data_sheet.write(row_idx, col_idx, str(cell_value), data_format)

                # Set column widths for full data sheet
                for col_idx, col_name in enumerate(df.columns):
                    max_width = max(len(str(col_name)), 10)  # Minimum width of 10
                    full_data_sheet.set_column(col_idx, col_idx, min(max_width, 50))  # Max width of 50

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'duplicates_complete_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Duplicate export error: {str(e)}")
                return jsonify({'error': f'Duplicate export failed: {str(e)}'}), 500

        @api.route('/independent-review/export/irregular-repetitions/', methods=['POST'])
        @self.require_auth
        def export_irregular_repetitions():
            """Export irregular repetitions details as Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                if not data or 'irregular_details' not in data:
                    return jsonify({'error': 'No irregular details provided'}), 400

                irregular_details = data['irregular_details']

                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)
                worksheet = workbook.add_worksheet('التكرارات الشاذة')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})

                # Write headers
                worksheet.write(0, 0, 'القيمة', header_format)
                worksheet.write(0, 1, 'عدد التكرار', header_format)
                worksheet.write(0, 2, 'النسبة المئوية', header_format)

                # Calculate total for percentage
                total_irregular = sum(irregular_details.values()) if isinstance(irregular_details, dict) else 0

                # Write irregular repetitions data
                row = 1
                if isinstance(irregular_details, dict):
                    for value, count in sorted(irregular_details.items(), key=lambda x: x[1], reverse=True):
                        percentage = (count / total_irregular * 100) if total_irregular > 0 else 0
                        worksheet.write(row, 0, str(value), data_format)
                        worksheet.write(row, 1, int(count), data_format)
                        worksheet.write(row, 2, f"{percentage:.2f}%", data_format)
                        row += 1

                # Set column widths
                worksheet.set_column(0, 0, 30)
                worksheet.set_column(1, 1, 15)
                worksheet.set_column(2, 2, 15)

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'irregular_repetitions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Irregular repetitions export error: {str(e)}")
                return jsonify({'error': f'Irregular repetitions export failed: {str(e)}'}), 500

        @api.route('/independent-review/export/unique/<field_name>', methods=['POST'])
        def export_unique_values(field_name):
            """Export unique values for a specific field to Excel"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                # Get uploaded file or use sample data
                df = None
                uploaded_files = request.files.getlist('files')

                if uploaded_files:
                    # Process uploaded file
                    file = uploaded_files[0]
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    elif file.filename.lower().endswith('.xlsx'):
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    elif file.filename.lower().endswith('.xls'):
                        df = pd.read_excel(file.stream, engine='xlrd')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                else:
                    # Use sample data from uploads directory
                    import os
                    upload_dir = 'uploads'
                    if os.path.exists(upload_dir):
                        sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
                        if sample_files:
                            file_path = os.path.join(upload_dir, sample_files[0])
                            if file_path.endswith('.csv'):
                                df = pd.read_csv(file_path, encoding='utf-8')
                            else:
                                df = pd.read_excel(file_path, engine='openpyxl')

                if df is None or df.empty:
                    return jsonify({'error': 'لا توجد بيانات متاحة للتصدير'}), 400

                # Fix Arabic text in column names
                df = process_dataframe_arabic_text(df)
                df.columns = [fix_arabic_text(col) for col in df.columns]

                # Check if field exists
                if field_name not in df.columns:
                    # Try partial match
                    matching_cols = [col for col in df.columns if field_name.lower() in col.lower()]
                    if matching_cols:
                        field_name = matching_cols[0]
                        logger.info(f"Found matching field: {field_name}")
                    else:
                        return jsonify({
                            'error': f'الحقل غير موجود: {field_name}',
                            'available_fields': list(df.columns)
                        }), 400

                # Get unique values for the field
                unique_values = df[field_name].dropna().unique()
                unique_count = len(unique_values)

                if unique_count == 0:
                    return jsonify({'error': f'لا توجد قيم فريدة في الحقل: {field_name}'}), 400

                # Create Excel file
                from io import BytesIO
                buffer = BytesIO()
                workbook = xlsxwriter.Workbook(buffer)
                worksheet = workbook.add_worksheet('القيم الفريدة')

                header_format = workbook.add_format({'bold': True, 'bg_color': '#4BACC6', 'font_color': 'white', 'border': 1})
                data_format = workbook.add_format({'border': 1, 'text_wrap': True})

                # Write headers
                worksheet.write(0, 0, 'القيمة الفريدة', header_format)
                worksheet.write(0, 1, 'عدد التكرار', header_format)

                # Write unique values with their counts
                row = 1
                for value in sorted(unique_values):
                    count = (df[field_name] == value).sum()
                    worksheet.write(row, 0, str(value), data_format)
                    worksheet.write(row, 1, int(count), data_format)
                    row += 1

                # Set column widths
                worksheet.set_column(0, 0, 40)
                worksheet.set_column(1, 1, 15)

                # Add summary information
                summary_format = workbook.add_format({'bold': True, 'bg_color': '#E6E6FA', 'border': 1})
                worksheet.write(0, 3, 'ملخص الحقل', summary_format)
                worksheet.write(1, 3, f'إجمالي القيم الفريدة: {unique_count}', data_format)
                worksheet.write(2, 3, f'إجمالي السجلات: {len(df)}', data_format)
                worksheet.write(3, 3, f'نسبة القيم الفريدة: {unique_count/len(df)*100:.1f}%', data_format)

                worksheet.set_column(3, 3, 25)

                workbook.close()
                buffer.seek(0)
                excel_data = buffer.getvalue()
                buffer.close()

                excel_buffer = BytesIO(excel_data)
                excel_buffer.seek(0)

                return send_file(
                    excel_buffer,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'unique_values_{field_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

            except Exception as e:
                logger.error(f"Unique values export error: {str(e)}")
                return jsonify({'error': f'فشل في تصدير القيم الفريدة: {str(e)}'}), 500

        @api.route('/independent-review/export/unique-values/excel', methods=['POST'])
        @self.require_auth
        def export_unique_values_excel():
            """Export unique values for a specific field to Excel (alternative endpoint)"""
            try:
                if not XLSXWRITER_AVAILABLE:
                    return jsonify({'error': 'Excel export not available - XlsxWriter not installed'}), 500

                data = request.get_json()
                if not data or 'field_name' not in data:
                    return jsonify({'error': 'Field name is required'}), 400

                field_name = data['field_name']

                # Call the existing export_unique_values function logic
                return export_unique_values(field_name)

            except Exception as e:
                logger.error(f"Unique values Excel export error: {str(e)}")
                return jsonify({'error': f'فشل في تصدير القيم الفريدة: {str(e)}'}), 500

        @api.route('/independent-review/search', methods=['POST'])
        def search_field_values():
            """Search for specific values in fields based on criteria"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'بيانات البحث مطلوبة'}), 400

                field_name = data.get('field_name', '').strip()
                search_value = data.get('search_value', '').strip()
                search_criteria = data.get('criteria', 'contains')  # contains, equals, starts_with, ends_with
                case_sensitive = data.get('case_sensitive', False)
                max_results = min(int(data.get('max_results', 100)), 1000)  # Limit to 1000 results

                if not field_name:
                    return jsonify({'error': 'اسم الحقل مطلوب'}), 400

                # Get uploaded files or use sample data
                uploaded_files = request.files.getlist('files')
                df = None

                if uploaded_files:
                    # Process uploaded file
                    file = uploaded_files[0]
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    elif file.filename.lower().endswith('.xlsx'):
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    elif file.filename.lower().endswith('.xls'):
                        df = pd.read_excel(file.stream, engine='xlrd')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                else:
                    # Use sample data from uploads directory
                    import os
                    upload_dir = 'uploads'
                    if os.path.exists(upload_dir):
                        sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
                        if sample_files:
                            file_path = os.path.join(upload_dir, sample_files[0])
                            if file_path.endswith('.csv'):
                                df = pd.read_csv(file_path, encoding='utf-8')
                            else:
                                df = pd.read_excel(file_path, engine='openpyxl')

                if df is None or df.empty:
                    return jsonify({'error': 'لا توجد بيانات متاحة للبحث'}), 400

                # Fix Arabic text in column names
                df = process_dataframe_arabic_text(df)
                df.columns = [fix_arabic_text(col) for col in df.columns]

                # Check if field exists
                if field_name not in df.columns:
                    # Try partial match
                    matching_cols = [col for col in df.columns if field_name.lower() in col.lower()]
                    if matching_cols:
                        field_name = matching_cols[0]
                        logger.info(f"Found matching field: {field_name}")
                    else:
                        return jsonify({
                            'error': f'الحقل غير موجود: {field_name}',
                            'available_fields': list(df.columns)
                        }), 400

                # Get the column data
                column_data = df[field_name].dropna()

                if column_data.empty:
                    return jsonify({
                        'field_name': field_name,
                        'search_value': search_value,
                        'criteria': search_criteria,
                        'results': [],
                        'total_matches': 0,
                        'message': 'الحقل فارغ'
                    })

                # Convert to string for text search
                str_data = column_data.astype(str)

                # Apply case sensitivity
                if not case_sensitive:
                    str_data = str_data.str.lower()
                    search_value = search_value.lower()

                # Apply search criteria
                matches = pd.Series([False] * len(str_data), index=str_data.index)

                if search_criteria == 'contains':
                    matches = str_data.str.contains(search_value, na=False, regex=False)
                elif search_criteria == 'equals':
                    matches = str_data == search_value
                elif search_criteria == 'starts_with':
                    matches = str_data.str.startswith(search_value)
                elif search_criteria == 'ends_with':
                    matches = str_data.str.endswith(search_value)
                elif search_criteria == 'regex':
                    try:
                        matches = str_data.str.contains(search_value, na=False, regex=True)
                    except Exception as regex_error:
                        return jsonify({'error': f'خطأ في التعبير النمطي: {str(regex_error)}'}), 400
                else:
                    return jsonify({'error': f'معيار البحث غير مدعوم: {search_criteria}'}), 400

                # Get matching values
                matching_values = column_data[matches]

                # Limit results
                if len(matching_values) > max_results:
                    matching_values = matching_values.head(max_results)

                # Get value counts for summary
                value_counts = matching_values.value_counts().head(20)  # Top 20 most frequent

                # Prepare results
                results = []
                for idx, value in matching_values.items():
                    results.append({
                        'row_index': int(idx),
                        'value': str(value),
                        'original_value': value if not pd.isna(value) else None
                    })

                # Get field statistics
                field_stats = {
                    'total_rows': len(df),
                    'non_null_values': len(column_data),
                    'unique_values': len(column_data.unique()),
                    'data_type': str(column_data.dtype)
                }

                return jsonify({
                    'field_name': field_name,
                    'search_value': search_value,
                    'criteria': search_criteria,
                    'case_sensitive': case_sensitive,
                    'total_matches': len(matching_values),
                    'results': results,
                    'value_summary': value_counts.to_dict(),
                    'field_stats': field_stats,
                    'search_timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"[SEARCH] Error in field search: {str(e)}")
                return jsonify({'error': f'خطأ في البحث: {str(e)}'}), 500

        @api.route('/independent-review/search/bulk', methods=['POST'])
        def bulk_search_fields():
            """Search across multiple fields with different criteria"""
            try:
                data = request.get_json()
                if not data or 'searches' not in data:
                    return jsonify({'error': 'بيانات البحث المتعدد مطلوبة'}), 400

                searches = data['searches']
                if not isinstance(searches, list) or len(searches) == 0:
                    return jsonify({'error': 'قائمة البحث يجب أن تحتوي على عنصر واحد على الأقل'}), 400

                # Get data
                uploaded_files = request.files.getlist('files')
                df = None

                if uploaded_files:
                    file = uploaded_files[0]
                    if file.filename.lower().endswith('.csv'):
                        df = pd.read_csv(file.stream, encoding='utf-8')
                    elif file.filename.lower().endswith('.xlsx'):
                        df = pd.read_excel(file.stream, engine='openpyxl')
                    elif file.filename.lower().endswith('.xls'):
                        df = pd.read_excel(file.stream, engine='xlrd')
                    else:
                        df = pd.read_excel(file.stream, engine='openpyxl')
                else:
                    # Use sample data
                    import os
                    upload_dir = 'uploads'
                    if os.path.exists(upload_dir):
                        sample_files = [f for f in os.listdir(upload_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
                        if sample_files:
                            file_path = os.path.join(upload_dir, sample_files[0])
                            if file_path.endswith('.csv'):
                                df = pd.read_csv(file_path, encoding='utf-8')
                            else:
                                df = pd.read_excel(file_path, engine='openpyxl')

                if df is None or df.empty:
                    return jsonify({'error': 'لا توجد بيانات متاحة للبحث'}), 400

                # Fix Arabic text
                df = process_dataframe_arabic_text(df)
                df.columns = [fix_arabic_text(col) for col in df.columns]

                bulk_results = []

                for search_config in searches:
                    field_name = search_config.get('field_name', '').strip()
                    search_value = search_config.get('search_value', '').strip()
                    criteria = search_config.get('criteria', 'contains')
                    case_sensitive = search_config.get('case_sensitive', False)
                    max_results = min(int(search_config.get('max_results', 50)), 200)

                    if not field_name:
                        bulk_results.append({
                            'field_name': field_name,
                            'error': 'اسم الحقل مطلوب'
                        })
                        continue

                    # Check if field exists
                    if field_name not in df.columns:
                        matching_cols = [col for col in df.columns if field_name.lower() in col.lower()]
                        if matching_cols:
                            field_name = matching_cols[0]
                        else:
                            bulk_results.append({
                                'field_name': field_name,
                                'error': f'الحقل غير موجود',
                                'available_fields': list(df.columns)
                            })
                            continue

                    # Perform search
                    column_data = df[field_name].dropna()
                    if column_data.empty:
                        bulk_results.append({
                            'field_name': field_name,
                            'search_value': search_value,
                            'total_matches': 0,
                            'results': [],
                            'message': 'الحقل فارغ'
                        })
                        continue

                    str_data = column_data.astype(str)

                    if not case_sensitive:
                        str_data = str_data.str.lower()
                        search_value = search_value.lower()

                    matches = pd.Series([False] * len(str_data), index=str_data.index)

                    if criteria == 'contains':
                        matches = str_data.str.contains(search_value, na=False, regex=False)
                    elif criteria == 'equals':
                        matches = str_data == search_value
                    elif criteria == 'starts_with':
                        matches = str_data.str.startswith(search_value)
                    elif criteria == 'ends_with':
                        matches = str_data.str.endswith(search_value)

                    matching_values = column_data[matches].head(max_results)

                    results = []
                    for idx, value in matching_values.items():
                        results.append({
                            'row_index': int(idx),
                            'value': str(value)
                        })

                    bulk_results.append({
                        'field_name': field_name,
                        'search_value': search_value,
                        'criteria': criteria,
                        'total_matches': len(matching_values),
                        'results': results
                    })

                return jsonify({
                    'bulk_search_results': bulk_results,
                    'total_searches': len(searches),
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"[BULK_SEARCH] Error in bulk field search: {str(e)}")
                return jsonify({'error': f'خطأ في البحث المتعدد: {str(e)}'}), 500

        return api

    def analyze_numeric_file(self, file_path: str, filename: str) -> Dict[str, Any]:
        """تحليل الملف الرقمي"""
        try:
            # قراءة البيانات
            import pandas as pd
            if filename.endswith(('.xlsx', '.xls')):
                try:
                    df = pd.read_excel(file_path, engine='openpyxl')
                except Exception as e:
                    error_msg = str(e).lower()
                    if 'zip' in error_msg or 'badzipfile' in error_msg or 'not a zip file' in error_msg:
                        raise ValueError("الملف المرفوع ليس ملف Excel صحيح (.xlsx) أو قد يكون تالفاً. يرجى التأكد من نوع الملف ومحاولة إعادة الرفع.")
                    else:
                        raise ValueError(f"خطأ في قراءة ملف Excel: {str(e)}")
            else:
                df = pd.read_csv(file_path)

            # كشف القطاع
            detected_sector, confidence, validation_message = self.sector_manager.detect_sector(df, filename)

            # الحصول على المحلل المناسب
            analyzer = self.sector_manager.get_analyzer(detected_sector)

            # تحديد أنواع الأعمدة
            column_types = {}
            for col in df.columns:
                if df[col].dtype in ['int64', 'float64']:
                    column_types[col] = 'number'
                else:
                    column_types[col] = 'text'

            # إجراء التحليل الرقمي - استخدم analyze بدلاً من analyze_numeric
            numeric_result = analyzer.analyze(df, column_types)

            return {
                'success': True,
                'detected_sector': detected_sector,
                'confidence': confidence,
                'numeric_analysis': numeric_result,
                'file_info': {
                    'name': filename,
                    'rows': len(df),
                    'columns': len(df.columns)
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"[API] خطأ في تحليل الملف الرقمي: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def require_auth(self, f: Callable) -> Callable:
        """ديكوراتور للتحقق من المصادقة"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = self.get_token_from_request()

            if not token:
                return jsonify({'error': 'رمز المصادقة مطلوب'}), 401

            try:
                payload = pyjwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
                g.user_id = payload['user_id']
                g.username = payload['username']
            except pyjwt.ExpiredSignatureError:
                return jsonify({'error': 'انتهت صلاحية رمز المصادقة'}), 401
            except pyjwt.InvalidTokenError:
                return jsonify({'error': 'رمز المصادقة غير صحيح'}), 401

            return f(*args, **kwargs)
        return decorated_function

    def rate_limit(self, f: Callable) -> Callable:
        """ديكوراتور للتحكم في معدل الطلبات"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # تنفيذ منطق rate limiting
            # يمكن استخدام Redis لتخزين عدادات الطلبات
            return f(*args, **kwargs)
        return decorated_function

    def get_token_from_request(self) -> Optional[str]:
        """الحصول على الرمز من طلب HTTP"""
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1]
        return None

    def generate_token(self, username: str) -> str:
        """إنشاء رمز JWT"""
        payload = {
            'user_id': str(uuid.uuid4()),  # يمكن استبداله بمعرف المستخدم الحقيقي
            'username': username,
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry),
            'iat': datetime.utcnow()
        }

        token = pyjwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        return token

    def authenticate_user(self, username: str, password: str) -> bool:
        """مصادقة المستخدم"""
        # تنفيذ منطق المصادقة الحقيقي
        # يمكن ربطه بقاعدة البيانات أو خدمة خارجية

        # placeholder - يقبل أي اسم مستخدم مع كلمة مرور "password"
        return password == "password"

    def save_temp_file(self, file) -> str:
        """حفظ الملف مؤقتاً"""
        try:
            temp_dir = os.path.join(os.getcwd(), 'temp_api')
            os.makedirs(temp_dir, exist_ok=True)

            # Generate secure filename
            original_filename = file.filename or "uploaded_file"
            secure_filename = f"{uuid.uuid4()}_{original_filename.replace('..', '').replace('/', '').replace('\\\\', '')}"
            temp_path = os.path.join(temp_dir, secure_filename)

            file.save(temp_path)

            # Verify file was saved correctly
            if not os.path.exists(temp_path):
                raise IOError("فشل في حفظ الملف المؤقت")

            return temp_path

        except Exception as e:
            logger.error(f"[API] خطأ في حفظ الملف المؤقت: {e}")
            raise IOError(f"فشل في حفظ الملف المؤقت: {str(e)}")

    def analyze_file(self, file_path: str, filename: str) -> Dict[str, Any]:
        """تحليل الملف مع تحسينات الأداء والدقة ودعم الملفات الكبيرة"""
        start_time = time.time()
        logger.info(f"[PERF] Starting enhanced file analysis for {filename}")

        try:
            # Validate file path and filename
            if not file_path or not isinstance(file_path, str):
                raise ValueError("مسار الملف مطلوب ويجب أن يكون نصاً")
            if not filename or not isinstance(filename, str):
                raise ValueError("اسم الملف مطلوب ويجب أن يكون نصاً")

            # Check if file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")

            # Check file size to determine processing strategy
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            use_streaming = file_size_mb > 2 and self.streaming_processor is not None  # Use streaming for files > 2MB if available

            if use_streaming:
                logger.info(f"[STREAMING] File size {file_size_mb:.1f}MB exceeds threshold, using streaming processor")
                return self._analyze_file_with_streaming(file_path, filename)
            else:
                logger.info(f"[STANDARD] File size {file_size_mb:.1f}MB, using standard processing")
                return self._analyze_file_standard(file_path, filename)

        except FileNotFoundError as e:
            total_time = time.time() - start_time
            logger.error(f"[API] ملف غير موجود بعد {total_time:.3f}s: {e}")
            return {
                'success': False,
                'error': f'الملف غير موجود: {str(e)}',
                'performance': {'total_time': round(total_time, 3)}
            }
        except ValueError as e:
            total_time = time.time() - start_time
            logger.error(f"[API] خطأ في التحقق من صحة البيانات بعد {total_time:.3f}s: {e}")
            return {
                'success': False,
                'error': f'خطأ في البيانات: {str(e)}',
                'performance': {'total_time': round(total_time, 3)}
            }
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"[API] خطأ في تحليل الملف بعد {total_time:.3f}s: {e}")
            return {
                'success': False,
                'error': str(e),
                'performance': {'total_time': round(total_time, 3)}
            }

    def _analyze_file_standard(self, file_path: str, filename: str) -> Dict[str, Any]:
        """تحليل الملف بالطريقة التقليدية للملفات الصغيرة"""
        start_time = time.time()
        logger.info(f"[STANDARD] Starting standard file analysis for {filename}")

        # قراءة البيانات مع تحسينات الأداء
        import pandas as pd
        read_start = time.time()

        # تحسين قراءة الملفات الكبيرة
        if filename.lower().endswith('.xlsx'):
            # استخدام محرك أسرع للـ Excel
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
            except Exception as e:
                error_msg = str(e).lower()
                if 'zip' in error_msg or 'badzipfile' in error_msg or 'not a zip file' in error_msg:
                    raise ValueError("الملف المرفوع ليس ملف Excel صحيح (.xlsx) أو قد يكون تالفاً. يرجى التأكد من نوع الملف ومحاولة إعادة الرفع.")
                else:
                    raise ValueError(f"خطأ في قراءة ملف Excel: {str(e)}")
        elif filename.lower().endswith('.xls'):
            try:
                df = pd.read_excel(file_path, engine='xlrd')
            except Exception as e:
                raise ValueError(f"خطأ في قراءة ملف Excel القديم (.xls): {str(e)}")
        else:
            # تحسين قراءة CSV مع كشف الترميز التلقائي
            try:
                # محاولة قراءة مع ترميز UTF-8 أولاً
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    # محاولة مع ترميز Windows-1256 (العربية)
                    df = pd.read_csv(file_path, encoding='cp1256')
                except UnicodeDecodeError:
                    # استخدام الترميز الافتراضي
                    df = pd.read_csv(file_path)

        read_time = time.time() - read_start
        logger.info(f"[PERF] Standard file reading took {read_time:.3f}s - Shape: {df.shape}")

        # Validate DataFrame
        if df.empty:
            raise ValueError("الملف فارغ أو لا يحتوي على بيانات صحيحة")

        # تنظيف البيانات الأولي
        cleanup_start = time.time()
        df = self._preprocess_dataframe(df)
        cleanup_time = time.time() - cleanup_start
        logger.info(f"[PERF] Data preprocessing took {cleanup_time:.3f}s")

        # كشف القطاع مع تحسينات
        sector_start = time.time()
        detected_sector, confidence, validation_message = self.sector_manager.detect_sector(df, filename)
        sector_time = time.time() - sector_start
        logger.info(f"[PERF] Standard sector detection took {sector_time:.3f}s - Sector: {detected_sector}")

        # الحصول على المحلل المناسب
        analyzer = self.sector_manager.get_analyzer(detected_sector)

        # تحديد أنواع الأعمدة مع تحسينات
        column_types_start = time.time()
        column_types = self._detect_column_types_enhanced(df)
        column_types_time = time.time() - column_types_start
        logger.info(f"[PERF] Standard column type detection took {column_types_time:.3f}s")

        # إجراء التحليل مع تحسينات
        analysis_start = time.time()
        analysis_result = analyzer.analyze(df, column_types)

        # إضافة تحليلات إضافية
        enhanced_analysis = self._enhance_analysis_results(df, analysis_result, column_types)
        analysis_time = time.time() - analysis_start
        logger.info(f"[PERF] Standard analysis execution took {analysis_time:.3f}s")

        total_time = time.time() - start_time
        logger.info(f"[PERF] Total standard analysis time: {total_time:.3f}s")

        # إضافة معلومات إضافية عن جودة البيانات
        data_quality_score = self._calculate_overall_data_quality(df, enhanced_analysis)

        return {
            'success': True,
            'detected_sector': detected_sector,
            'confidence': confidence,
            'validation_message': validation_message,
            'analysis': enhanced_analysis,
            'data_quality': {
                'overall_score': data_quality_score,
                'grade': self._get_quality_grade(data_quality_score),
                'recommendations': self._generate_quality_recommendations(df, enhanced_analysis)
            },
            'file_info': {
                'name': filename,
                'rows': len(df),
                'columns': len(df.columns),
                'size_mb': round(os.path.getsize(file_path) / (1024 * 1024), 2),
                'encoding_detected': self._detect_file_encoding(file_path)
            },
            'performance': {
                'total_time': round(total_time, 3),
                'read_time': round(read_time, 3),
                'cleanup_time': round(cleanup_time, 3),
                'sector_detection_time': round(sector_time, 3),
                'column_types_time': round(column_types_time, 3),
                'analysis_time': round(analysis_time, 3)
            },
            'processing_stats': {
                'memory_usage_mb': round(df.memory_usage(deep=True).sum() / (1024 * 1024), 2),
                'null_percentage': round(df.isnull().sum().sum() / (len(df) * len(df.columns)) * 100, 2),
                'duplicate_rows': len(df) - len(df.drop_duplicates())
            },
            'timestamp': datetime.now().isoformat()
        }

    def _analyze_file_with_streaming(self, file_path: str, filename: str) -> Dict[str, Any]:
        """تحليل الملف باستخدام معالج البيانات المتدفق للملفات الكبيرة"""
        start_time = time.time()
        logger.info(f"[STREAMING] Starting streaming file analysis for {filename}")

        try:
            # وظيفة المعالجة التي سيتم تطبيقها على كل دفعة
            def process_chunk(df_chunk):
                """معالجة دفعة واحدة من البيانات"""
                try:
                    # تنظيف البيانات الأولي
                    df_chunk = self._preprocess_dataframe(df_chunk)

                    # كشف القطاع (للدفعة الأولى فقط إذا أمكن)
                    detected_sector, confidence, validation_message = self.sector_manager.detect_sector(df_chunk, filename)

                    # الحصول على المحلل المناسب
                    analyzer = self.sector_manager.get_analyzer(detected_sector)

                    # تحديد أنواع الأعمدة
                    column_types = self._detect_column_types_enhanced(df_chunk)

                    # إجراء التحليل
                    analysis_result = analyzer.analyze(df_chunk, column_types)

                    # إضافة تحليلات إضافية
                    enhanced_analysis = self._enhance_analysis_results(df_chunk, analysis_result, column_types)

                    return {
                        'chunk_info': {
                            'rows': len(df_chunk),
                            'columns': len(df_chunk.columns),
                            'detected_sector': detected_sector,
                            'confidence': confidence
                        },
                        'analysis': enhanced_analysis,
                        'data_quality': {
                            'score': self._calculate_overall_data_quality(df_chunk, enhanced_analysis)
                        },
                        'processing_stats': {
                            'memory_usage_mb': round(df_chunk.memory_usage(deep=True).sum() / (1024 * 1024), 2),
                            'null_percentage': round(df_chunk.isnull().sum().sum() / (len(df_chunk) * len(df_chunk.columns)) * 100, 2),
                            'duplicate_rows': len(df_chunk) - len(df_chunk.drop_duplicates())
                        }
                    }

                except Exception as e:
                    logger.error(f"[STREAMING] Error processing chunk: {e}")
                    return {
                        'error': str(e),
                        'chunk_info': {'rows': len(df_chunk) if hasattr(df_chunk, '__len__') else 0}
                    }

            # استخدام معالج البيانات المتدفق
            streaming_result = self.streaming_processor.process_large_file(
                file_path=file_path,
                processing_func=process_chunk
            )

            if not streaming_result.get('success', False):
                raise Exception(f"Streaming processor failed: {streaming_result.get('error', 'Unknown error')}")

            # دمج نتائج الدفعات
            merged_result = self._merge_streaming_results(streaming_result)

            total_time = time.time() - start_time
            logger.info(f"[STREAMING] Total streaming analysis time: {total_time:.3f}s")

            # إضافة معلومات الأداء والمعالجة
            merged_result.update({
                'file_info': {
                    'name': filename,
                    'size_mb': round(os.path.getsize(file_path) / (1024 * 1024), 2),
                    'encoding_detected': self._detect_file_encoding(file_path),
                    'processing_method': 'streaming'
                },
                'performance': {
                    'total_time': round(total_time, 3),
                    'streaming_time': round(streaming_result.get('processing_time', 0), 3),
                    'method': streaming_result.get('method', 'unknown')
                },
                'streaming_stats': {
                    'chunks_processed': streaming_result.get('chunks_processed', 0),
                    'rows_processed': streaming_result.get('rows_processed', 0),
                    'memory_peak_mb': streaming_result.get('memory_peak_mb', 0),
                    'temp_files_created': streaming_result.get('temp_files_created', 0)
                },
                'timestamp': datetime.now().isoformat()
            })

            return merged_result

        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"[STREAMING] Error in streaming analysis after {total_time:.3f}s: {e}")
            return {
                'success': False,
                'error': f'خطأ في المعالجة المتدفقة: {str(e)}',
                'performance': {'total_time': round(total_time, 3)},
                'processing_method': 'streaming_failed'
            }

    def _merge_streaming_results(self, streaming_result: Dict[str, Any]) -> Dict[str, Any]:
        """دمج نتائج المعالجة المتدفقة"""
        try:
            result_data = streaming_result.get('result', {})

            if not result_data:
                return {
                    'success': False,
                    'error': 'لا توجد نتائج من المعالجة المتدفقة'
                }

            # إذا كانت النتيجة قائمة من النتائج
            if isinstance(result_data, list):
                # دمج إحصائيات الدفعات
                total_rows = 0
                total_columns = 0
                sectors_found = {}
                quality_scores = []
                all_analysis = []

                for chunk_result in result_data:
                    if 'error' in chunk_result:
                        continue

                    chunk_info = chunk_result.get('chunk_info', {})
                    total_rows += chunk_info.get('rows', 0)
                    total_columns = max(total_columns, chunk_info.get('columns', 0))

                    sector = chunk_info.get('detected_sector')
                    if sector:
                        sectors_found[sector] = sectors_found.get(sector, 0) + 1

                    quality_scores.append(chunk_result.get('data_quality', {}).get('score', 0))
                    all_analysis.append(chunk_result.get('analysis', {}))

                # تحديد القطاع الأكثر شيوعاً
                detected_sector = max(sectors_found.keys(), key=lambda k: sectors_found[k]) if sectors_found else 'unknown'
                avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0

                # دمج التحليلات (يمكن تحسين هذا حسب نوع التحليل)
                merged_analysis = self._merge_analysis_results(all_analysis)

                return {
                    'success': True,
                    'detected_sector': detected_sector,
                    'confidence': 0.8,  # ثقة متوسطة للمعالجة المتدفقة
                    'validation_message': f'تم تحليل {len(result_data)} دفعة باستخدام المعالجة المتدفقة',
                    'analysis': merged_analysis,
                    'data_quality': {
                        'overall_score': avg_quality_score,
                        'grade': self._get_quality_grade(avg_quality_score),
                        'recommendations': ['تم استخدام المعالجة المتدفقة للملف الكبير']
                    },
                    'file_info': {
                        'total_rows': total_rows,
                        'total_columns': total_columns,
                        'chunks_processed': len(result_data)
                    }
                }

            # إذا كانت النتيجة كائن واحد
            elif isinstance(result_data, dict):
                return {
                    'success': True,
                    'detected_sector': result_data.get('chunk_info', {}).get('detected_sector', 'unknown'),
                    'confidence': result_data.get('chunk_info', {}).get('confidence', 0.5),
                    'analysis': result_data.get('analysis', {}),
                    'data_quality': result_data.get('data_quality', {}),
                    'processing_stats': result_data.get('processing_stats', {})
                }

            else:
                return {
                    'success': False,
                    'error': f'نوع نتيجة غير مدعوم: {type(result_data)}'
                }

        except Exception as e:
            logger.error(f"[STREAMING] Error merging streaming results: {e}")
            return {
                'success': False,
                'error': f'خطأ في دمج النتائج: {str(e)}'
            }

    def _merge_analysis_results(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """دمج نتائج التحليل من دفعات متعددة"""
        try:
            if not analysis_results:
                return {}

            merged = {}

            # دمج الإحصائيات الأساسية
            for analysis in analysis_results:
                for key, value in analysis.items():
                    if key not in merged:
                        merged[key] = value
                    elif isinstance(value, (int, float)) and isinstance(merged[key], (int, float)):
                        merged[key] += value  # جمع القيم الرقمية
                    elif isinstance(value, list) and isinstance(merged[key], list):
                        merged[key].extend(value)  # دمج القوائم
                    elif isinstance(value, dict) and isinstance(merged[key], dict):
                        # دمج القواميس (يمكن تحسين هذا)
                        merged[key].update(value)

            return merged

        except Exception as e:
            logger.warning(f"[STREAMING] Error merging analysis results: {e}")
            return {'error': f'خطأ في دمج نتائج التحليل: {str(e)}'}

    def analyze_file_async(self, task_id: str, file_path: str, filename: str):
        """تحليل الملف بشكل غير متزامن مع تحسينات الأداء"""
        try:
            result = self.analyze_file(file_path, filename)

            # حفظ النتيجة في التخزين المؤقت أو قاعدة البيانات
            cache = get_cache_manager()
            cache.set(f"analysis_result:{task_id}", result, ttl=3600)  # حفظ لساعة

            # حفظ معلومات المهمة للتتبع
            task_info = {
                'task_id': task_id,
                'filename': filename,
                'status': 'completed',
                'completed_at': datetime.now().isoformat(),
                'result_summary': {
                    'success': result.get('success', False),
                    'detected_sector': result.get('detected_sector'),
                    'data_quality_score': result.get('data_quality', {}).get('overall_score'),
                    'processing_time': result.get('performance', {}).get('total_time')
                }
            }
            cache.set(f"task_info:{task_id}", task_info, ttl=3600)

            # تنظيف الملف المؤقت
            try:
                os.remove(file_path)
            except OSError as e:
                logger.warning(f"[API] فشل في حذف الملف المؤقت {file_path}: {e}")

            logger.info(f"[API] تم إكمال التحليل غير المتزامن: {task_id}")

        except Exception as e:
            logger.error(f"[API] خطأ في التحليل غير المتزامن {task_id}: {e}")
            # حفظ رسالة الخطأ
            cache = get_cache_manager()
            error_result = {
                'success': False,
                'error': str(e),
                'task_id': task_id,
                'error_timestamp': datetime.now().isoformat()
            }
            cache.set(f"analysis_result:{task_id}", error_result, ttl=3600)

            # حفظ معلومات خطأ المهمة
            task_info = {
                'task_id': task_id,
                'filename': filename,
                'status': 'failed',
                'failed_at': datetime.now().isoformat(),
                'error': str(e)
            }
            cache.set(f"task_info:{task_id}", task_info, ttl=3600)

    def check_database_health(self) -> Dict[str, Any]:
        """فحص حالة قاعدة البيانات"""
        try:
            db = get_db_manager()
            # تنفيذ استعلام اختبار
            return {'status': 'healthy', 'response_time': 0.001}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

    def check_cache_health(self) -> Dict[str, Any]:
        """فحص حالة التخزين المؤقت"""
        try:
            cache = get_cache_manager()
            # اختبار الكتابة والقراءة
            test_key = f"health_check_{uuid.uuid4()}"
            cache.set(test_key, "test_value", ttl=10)
            result = cache.get(test_key)
            cache.delete(test_key)

            return {'status': 'healthy' if result == "test_value" else 'degraded'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

    def check_memory_health(self) -> Dict[str, Any]:
        """فحص حالة الذاكرة مع تحسينات"""
        try:
            perf_optimizer = get_performance_optimizer()
            memory_stats = perf_optimizer.get_performance_report().get('memory_analysis', {})

            memory_percent = memory_stats.get('system_memory', {}).get('percent', 0)
            status = 'healthy' if memory_percent < 80 else 'warning' if memory_percent < 90 else 'critical'

            # إضافة معلومات إضافية عن الذاكرة
            process_memory_mb = memory_stats.get('process_memory', {}).get('rss', 0) / (1024 * 1024)
            available_memory_gb = memory_stats.get('system_memory', {}).get('available', 0) / (1024 * 1024 * 1024)

            return {
                'status': status,
                'memory_percent': memory_percent,
                'process_memory_mb': round(process_memory_mb, 2),
                'available_memory_gb': round(available_memory_gb, 2),
                'recommendations': self._get_memory_recommendations(memory_percent, process_memory_mb),
                'details': memory_stats
            }
        except Exception as e:
            return {'status': 'unknown', 'error': str(e)}

    def _get_memory_recommendations(self, memory_percent: float, process_memory_mb: float) -> List[str]:
        """الحصول على توصيات لتحسين استخدام الذاكرة"""
        recommendations = []

        if memory_percent > 90:
            recommendations.append('استخدام الذاكرة مرتفع جداً - يُنصح بإغلاق التطبيقات الأخرى')
        elif memory_percent > 80:
            recommendations.append('استخدام الذاكرة مرتفع - يُنصح بمراقبة الاستخدام')

        if process_memory_mb > 1000:  # أكثر من 1GB
            recommendations.append('استخدام ذاكرة العملية مرتفع - يُنصح بتحسين معالجة البيانات الكبيرة')
        elif process_memory_mb > 500:  # أكثر من 500MB
            recommendations.append('استخدام ذاكرة العملية متوسط - يمكن تحسينه')

        if len(recommendations) == 0:
            recommendations.append('استخدام الذاكرة طبيعي')

        return recommendations

    def before_request(self):
        """middleware قبل كل طلب"""
        g.request_start_time = time.time()
        g.request_id = str(uuid.uuid4())

        logger.info(f"[API_REQUEST] {request.method} {request.path} - ID: {g.request_id}")

    def after_request(self, response):
        """middleware بعد كل طلب"""
        if hasattr(g, 'request_start_time'):
            duration = time.time() - g.request_start_time
            logger.info(f"[API_RESPONSE] {request.method} {request.path} - {response.status_code} - {duration:.3f}s")

        return response

    def handle_bad_request(self, error):
        """معالجة خطأ 400"""
        return jsonify({'error': 'طلب غير صحيح', 'code': 400}), 400

    def handle_unauthorized(self, error):
        """معالجة خطأ 401"""
        return jsonify({'error': 'غير مصرح لك بالوصول', 'code': 401}), 401

    def handle_forbidden(self, error):
        """معالجة خطأ 403"""
        return jsonify({'error': 'ممنوع الوصول', 'code': 403}), 403

    def handle_not_found(self, error):
        """معالجة خطأ 404"""
        requested_path = request.path
        logger.warning(f"[API_404] مسار غير موجود: {request.method} {requested_path}")
        return jsonify({'error': 'المسار غير موجود', 'code': 404}), 404

    def handle_internal_error(self, error):
        """معالجة خطأ 500"""
        logger.error(f"[API_ERROR] خطأ داخلي: {error}")
        return jsonify({'error': 'خطأ في الخادم', 'code': 500}), 500

    def start(self):
        """بدء تشغيل خادم API"""
        if self.running:
            return

        self.running = True

        if not self.app:
            self.app = self.create_app()

        logger.info(f"[API_SERVER] بدء تشغيل خادم API على {self.host}:{self.port}")

        # تشغيل الخادم في thread منفصل
        self.server_thread = threading.Thread(target=self._run_server, daemon=True)
        self.server_thread.start()

    def stop(self):
        """إيقاف خادم API"""
        self.running = False
        logger.info("[API_SERVER] تم إيقاف خادم API")

    def _run_server(self):
        """تشغيل خادم Flask مع تحسينات"""
        try:
            if not self.app:
                self.app = self.create_app()

            # تسجيل وقت البدء
            self._start_time = time.time()
            self._active_connections = 0

            logger.info(f"[API_SERVER] بدء تشغيل الخادم المحسن على {self.host}:{self.port}")

            self.app.run(
                host=self.host,
                port=self.port,
                debug=self.debug,
                threaded=True,
                use_reloader=False
            )
        except Exception as e:
            logger.error(f"[API_SERVER] خطأ في تشغيل الخادم: {e}")
            self.running = False

    def get_server_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخادم"""
        return {
            'running': self.running,
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'uptime': time.time() - getattr(self, '_start_time', time.time()),
            'active_connections': getattr(self, '_active_connections', 0)
        }
    def is_running(self) -> bool:
        """فحص ما إذا كان الخادم يعمل"""
        return self.running

# دوال مساعدة للاستخدام السهل
def create_api_server(config: Dict[str, Any]) -> APIServer:
    """إنشاء خادم API"""
    return APIServer(config)

def start_api_server(config: Dict[str, Any]) -> APIServer:
    """بدء تشغيل خادم API"""
    server = APIServer(config)
    server.start()
    return server

# مثال على الاستخدام
if __name__ == "__main__":
    # إعدادات API
    api_config = {
        'host': '0.0.0.0',
        'port': 2000,
        'debug': True,
        'jwt_secret': 'your-secret-key-change-in-production',
        'token_expiry_hours': 24,
        'cors_origins': ['*'],
        'rate_limiting': {
            'enabled': True,
            'max_requests_per_minute': 60
        },
        'streaming': {
            'chunk_size': 10000,
            'max_memory_mb': 512
        }
    }

    # إنشاء وبدء تشغيل الخادم
    server = start_api_server(api_config)

    print("خادم API يعمل على http://0.0.0.0:8001")
    print("API Documentation: http://0.0.0.0:8001/api/v1/health")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nإيقاف خادم API...")
        server.stop()